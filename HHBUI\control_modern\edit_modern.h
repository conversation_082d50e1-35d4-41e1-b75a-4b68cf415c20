/**
** =====================================================================================
**
**       文件名称: edit_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化编辑框控件 - 编辑框控件 （声明文件）
**
**       主要功能:
**       - 现代化文本编辑与输入体验
**       - 高性能文本渲染与编辑操作
**       - 智能文本验证与格式化系统
**       - 异常安全的编辑操作接口
**       - 跨平台文本编辑抽象与兼容
**       - 编辑性能监控与调试支持
**       - 现代化文本选择与操作机制
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与异常安全保证
**       - 高性能文本处理与渲染
**       - 模板元编程与编译时优化
**       - 多线程安全的编辑操作
**       - 标准库容器与算法集成
**       - 跨平台文本编辑抽象层设计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化编辑框控件
**                             2. 实现智能文本处理机制
**                             3. 添加高性能渲染系统
**                             4. 集成异常安全保证机制
**                             5. 优化文本编辑性能
**                             6. 确保跨平台兼容性
**                             7. 添加编辑调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <functional>
#include <optional>
#include <regex>
#include <chrono>
#include <atomic>
#include <mutex>
#include "../ui_modern/control_base_modern.h"

namespace hhbui::modern {

    // 前向声明
    class Font_HHBUI;
    class TextValidator_HHBUI;
    class TextFormatter_HHBUI;
    class UndoRedoManager_HHBUI;

    /// 编辑框类型枚举
    enum class EditType_HHBUI : uint32_t {
        SingleLine = 0,     // 单行编辑框
        MultiLine = 1,      // 多行编辑框
        Password = 2,       // 密码编辑框
        Number = 3,         // 数字编辑框
        Email = 4,          // 邮箱编辑框
        Url = 5,            // URL编辑框
        Search = 6,         // 搜索编辑框
        RichText = 7        // 富文本编辑框
    };

    /// 文本对齐方式枚举
    enum class TextAlignment_HHBUI : uint32_t {
        Left = 0,
        Center = 1,
        Right = 2,
        Justify = 3
    };

    /// 输入模式枚举
    enum class InputMode_HHBUI : uint32_t {
        Normal = 0,         // 正常输入
        ReadOnly = 1,       // 只读模式
        Disabled = 2,       // 禁用模式
        Protected = 3       // 保护模式（可选择但不可编辑）
    };

    /// 文本选择信息
    struct TextSelection_HHBUI {
        size_t start = 0;           // 选择开始位置
        size_t end = 0;             // 选择结束位置
        bool hasSelection = false;   // 是否有选择

        [[nodiscard]] size_t length_HHBUI() const noexcept {
            return hasSelection ? (end > start ? end - start : start - end) : 0;
        }

        [[nodiscard]] bool isEmpty_HHBUI() const noexcept {
            return !hasSelection || start == end;
        }

        void clear_HHBUI() noexcept {
            start = end = 0;
            hasSelection = false;
        }
    };

    /// 编辑框事件类型
    enum class EditEvent_HHBUI : uint32_t {
        TextChanged = 0,
        SelectionChanged = 1,
        CursorMoved = 2,
        FocusGained = 3,
        FocusLost = 4,
        KeyPressed = 5,
        KeyReleased = 6,
        MouseClicked = 7,
        TextValidated = 8,
        UndoRedoChanged = 9
    };

    /// 编辑框事件处理器
    using EditEventHandler_HHBUI = Function<void(EditEvent_HHBUI, const Any&)>;

    /// 文本验证结果
    struct ValidationResult_HHBUI {
        bool isValid = true;
        String errorMessage;
        String suggestion;
        size_t errorPosition = 0;
    };

    /// 文本验证器接口
    class TextValidator_HHBUI {
    public:
        virtual ~TextValidator_HHBUI() = default;
        
        /// 验证文本
        [[nodiscard]] virtual ValidationResult_HHBUI validate_HHBUI(StringView text) const noexcept = 0;
        
        /// 验证字符输入
        [[nodiscard]] virtual bool validateChar_HHBUI(wchar_t ch, size_t position, StringView currentText) const noexcept = 0;
        
        /// 获取验证器名称
        [[nodiscard]] virtual StringView getName_HHBUI() const noexcept = 0;
    };

    /// 现代化编辑框控件类
    class Edit_HHBUI : public ControlBase_HHBUI {
    public:
        /// 构造函数
        explicit Edit_HHBUI(StringView name = L"HHBUI_Edit") noexcept;

        /// 析构函数
        ~Edit_HHBUI() noexcept override;

        /// 初始化编辑框
        [[nodiscard]] bool initialize_HHBUI(
            ControlBase_HHBUI* parent,
            const UIRect_HHBUI& bounds,
            EditType_HHBUI type = EditType_HHBUI::SingleLine,
            StringView placeholder = L"",
            int32_t id = 0
        ) noexcept;

        /// 设置编辑框类型
        void setEditType_HHBUI(EditType_HHBUI type) noexcept;

        /// 获取编辑框类型
        [[nodiscard]] EditType_HHBUI getEditType_HHBUI() const noexcept;

        /// 设置输入模式
        void setInputMode_HHBUI(InputMode_HHBUI mode) noexcept;

        /// 获取输入模式
        [[nodiscard]] InputMode_HHBUI getInputMode_HHBUI() const noexcept;

        /// 设置文本内容
        void setText_HHBUI(StringView text) noexcept override;

        /// 获取文本内容
        [[nodiscard]] const String& getText_HHBUI() const noexcept override;

        /// 获取纯文本内容（去除格式）
        [[nodiscard]] String getPlainText_HHBUI() const noexcept;

        /// 设置占位符文本
        void setPlaceholder_HHBUI(StringView placeholder) noexcept;

        /// 获取占位符文本
        [[nodiscard]] const String& getPlaceholder_HHBUI() const noexcept;

        /// 设置最大文本长度
        void setMaxLength_HHBUI(size_t maxLength) noexcept;

        /// 获取最大文本长度
        [[nodiscard]] size_t getMaxLength_HHBUI() const noexcept;

        /// 设置文本对齐方式
        void setTextAlignment_HHBUI(TextAlignment_HHBUI alignment) noexcept;

        /// 获取文本对齐方式
        [[nodiscard]] TextAlignment_HHBUI getTextAlignment_HHBUI() const noexcept;

        /// 设置字体
        void setFont_HHBUI(SharedPtr<Font_HHBUI> font) noexcept;

        /// 获取字体
        [[nodiscard]] SharedPtr<Font_HHBUI> getFont_HHBUI() const noexcept;

        /// 设置文本颜色
        void setTextColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取文本颜色
        [[nodiscard]] UIColor_HHBUI getTextColor_HHBUI() const noexcept;

        /// 设置选择背景颜色
        void setSelectionColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取选择背景颜色
        [[nodiscard]] UIColor_HHBUI getSelectionColor_HHBUI() const noexcept;

        /// 设置光标颜色
        void setCursorColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取光标颜色
        [[nodiscard]] UIColor_HHBUI getCursorColor_HHBUI() const noexcept;

        /// 设置光标位置
        void setCursorPosition_HHBUI(size_t position) noexcept;

        /// 获取光标位置
        [[nodiscard]] size_t getCursorPosition_HHBUI() const noexcept;

        /// 设置文本选择
        void setSelection_HHBUI(size_t start, size_t end) noexcept;

        /// 获取文本选择
        [[nodiscard]] TextSelection_HHBUI getSelection_HHBUI() const noexcept;

        /// 选择全部文本
        void selectAll_HHBUI() noexcept;

        /// 清除选择
        void clearSelection_HHBUI() noexcept;

        /// 获取选择的文本
        [[nodiscard]] String getSelectedText_HHBUI() const noexcept;

        /// 插入文本
        void insertText_HHBUI(StringView text) noexcept;

        /// 删除选择的文本
        void deleteSelection_HHBUI() noexcept;

        /// 替换选择的文本
        void replaceSelection_HHBUI(StringView text) noexcept;

        /// 复制到剪贴板
        void copy_HHBUI() noexcept;

        /// 剪切到剪贴板
        void cut_HHBUI() noexcept;

        /// 从剪贴板粘贴
        void paste_HHBUI() noexcept;

        /// 撤销操作
        void undo_HHBUI() noexcept;

        /// 重做操作
        void redo_HHBUI() noexcept;

        /// 检查是否可以撤销
        [[nodiscard]] bool canUndo_HHBUI() const noexcept;

        /// 检查是否可以重做
        [[nodiscard]] bool canRedo_HHBUI() const noexcept;

        /// 查找文本
        [[nodiscard]] Vector<size_t> findText_HHBUI(StringView searchText, bool caseSensitive = false) const noexcept;

        /// 替换文本
        [[nodiscard]] size_t replaceText_HHBUI(StringView searchText, StringView replaceText, bool replaceAll = false, bool caseSensitive = false) noexcept;

        /// 设置文本验证器
        void setValidator_HHBUI(UniquePtr<TextValidator_HHBUI> validator) noexcept;

        /// 获取文本验证器
        [[nodiscard]] TextValidator_HHBUI* getValidator_HHBUI() const noexcept;

        /// 验证当前文本
        [[nodiscard]] ValidationResult_HHBUI validateText_HHBUI() const noexcept;

        /// 启用/禁用自动完成
        void setAutoCompleteEnabled_HHBUI(bool enabled) noexcept;

        /// 检查自动完成是否启用
        [[nodiscard]] bool isAutoCompleteEnabled_HHBUI() const noexcept;

        /// 设置自动完成候选项
        void setAutoCompleteCandidates_HHBUI(const Vector<String>& candidates) noexcept;

        /// 获取自动完成候选项
        [[nodiscard]] const Vector<String>& getAutoCompleteCandidates_HHBUI() const noexcept;

        /// 启用/禁用拼写检查
        void setSpellCheckEnabled_HHBUI(bool enabled) noexcept;

        /// 检查拼写检查是否启用
        [[nodiscard]] bool isSpellCheckEnabled_HHBUI() const noexcept;

        /// 设置行高
        void setLineHeight_HHBUI(float lineHeight) noexcept;

        /// 获取行高
        [[nodiscard]] float getLineHeight_HHBUI() const noexcept;

        /// 设置制表符宽度
        void setTabWidth_HHBUI(size_t tabWidth) noexcept;

        /// 获取制表符宽度
        [[nodiscard]] size_t getTabWidth_HHBUI() const noexcept;

        /// 启用/禁用自动换行
        void setWordWrapEnabled_HHBUI(bool enabled) noexcept;

        /// 检查自动换行是否启用
        [[nodiscard]] bool isWordWrapEnabled_HHBUI() const noexcept;

        /// 获取行数
        [[nodiscard]] size_t getLineCount_HHBUI() const noexcept;

        /// 获取指定行的文本
        [[nodiscard]] String getLineText_HHBUI(size_t lineIndex) const noexcept;

        /// 滚动到指定位置
        void scrollTo_HHBUI(size_t position) noexcept;

        /// 滚动到指定行
        void scrollToLine_HHBUI(size_t lineIndex) noexcept;

        /// 注册编辑框事件处理器
        void registerEventHandler_HHBUI(EditEvent_HHBUI eventType, EditEventHandler_HHBUI handler) noexcept;

        /// 注销编辑框事件处理器
        void unregisterEventHandler_HHBUI(EditEvent_HHBUI eventType) noexcept;

    protected:
        /// 消息处理函数重写
        LRESULT onMessage_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept override;

        /// 绘制函数重写
        void onPaint_HHBUI(Canvas_HHBUI* canvas) noexcept override;

        /// 状态变更通知重写
        void onStateChanged_HHBUI(ControlState_HHBUI oldState, ControlState_HHBUI newState) noexcept override;

        /// 文本变更事件
        virtual void onTextChanged_HHBUI(StringView oldText, StringView newText) noexcept;

        /// 选择变更事件
        virtual void onSelectionChanged_HHBUI(const TextSelection_HHBUI& oldSelection, const TextSelection_HHBUI& newSelection) noexcept;

        /// 光标移动事件
        virtual void onCursorMoved_HHBUI(size_t oldPosition, size_t newPosition) noexcept;

        /// 字符输入事件
        virtual bool onCharInput_HHBUI(wchar_t ch) noexcept;

        /// 键盘按下事件
        virtual bool onKeyDown_HHBUI(uint32_t keyCode, uint32_t modifiers) noexcept;

        /// 键盘释放事件
        virtual bool onKeyUp_HHBUI(uint32_t keyCode, uint32_t modifiers) noexcept;

    private:
        /// 绘制文本内容
        void drawText_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制占位符
        void drawPlaceholder_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制光标
        void drawCursor_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制选择背景
        void drawSelection_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 计算光标位置
        [[nodiscard]] UIPoint_HHBUI calculateCursorPosition_HHBUI(size_t textPosition) const noexcept;

        /// 计算文本位置
        [[nodiscard]] size_t calculateTextPosition_HHBUI(const UIPoint_HHBUI& point) const noexcept;

        /// 更新光标闪烁
        void updateCursorBlink_HHBUI() noexcept;

        /// 触发编辑框事件
        void triggerEvent_HHBUI(EditEvent_HHBUI eventType, const Any& eventData = {}) noexcept;

        /// 处理文本输入
        void handleTextInput_HHBUI(StringView input) noexcept;

        /// 处理特殊键
        bool handleSpecialKey_HHBUI(uint32_t keyCode, uint32_t modifiers) noexcept;

        /// 更新撤销重做状态
        void updateUndoRedoState_HHBUI() noexcept;

    private:
        EditType_HHBUI editType_{EditType_HHBUI::SingleLine};
        InputMode_HHBUI inputMode_{InputMode_HHBUI::Normal};
        TextAlignment_HHBUI textAlignment_{TextAlignment_HHBUI::Left};
        
        String text_{};
        String placeholder_{};
        size_t maxLength_{SIZE_MAX};
        
        SharedPtr<Font_HHBUI> font_{};
        UIColor_HHBUI textColor_{0.0f, 0.0f, 0.0f, 1.0f};
        UIColor_HHBUI selectionColor_{0.0f, 0.5f, 1.0f, 0.3f};
        UIColor_HHBUI cursorColor_{0.0f, 0.0f, 0.0f, 1.0f};
        
        size_t cursorPosition_ = 0;
        TextSelection_HHBUI selection_{};
        
        UniquePtr<TextValidator_HHBUI> validator_{};
        UniquePtr<UndoRedoManager_HHBUI> undoRedoManager_{};
        
        std::atomic<bool> autoCompleteEnabled_{false};
        Vector<String> autoCompleteCandidates_{};
        
        std::atomic<bool> spellCheckEnabled_{false};
        std::atomic<bool> wordWrapEnabled_{false};
        
        float lineHeight_ = 1.2f;
        size_t tabWidth_ = 4;
        
        HashMap<EditEvent_HHBUI, Vector<EditEventHandler_HHBUI>> eventHandlers_{};
        mutable std::shared_mutex eventHandlersMutex_{};
        
        std::atomic<bool> cursorVisible_{true};
        std::chrono::steady_clock::time_point lastCursorBlink_{};
        std::chrono::milliseconds cursorBlinkInterval_{500};
    };

    /// 全局编辑框函数

    /// 创建编辑框
    [[nodiscard]] UniquePtr<Edit_HHBUI> createEdit_HHBUI(
        EditType_HHBUI type = EditType_HHBUI::SingleLine,
        StringView placeholder = L""
    ) noexcept;

    /// 创建密码编辑框
    [[nodiscard]] UniquePtr<Edit_HHBUI> createPasswordEdit_HHBUI(StringView placeholder = L"Password") noexcept;

    /// 创建数字编辑框
    [[nodiscard]] UniquePtr<Edit_HHBUI> createNumberEdit_HHBUI(StringView placeholder = L"0") noexcept;

    /// 创建多行编辑框
    [[nodiscard]] UniquePtr<Edit_HHBUI> createMultiLineEdit_HHBUI(StringView placeholder = L"") noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIEdit = hhbui::modern::Edit_HHBUI;
    using EditType = hhbui::modern::EditType_HHBUI;
    using TextAlignment = hhbui::modern::TextAlignment_HHBUI;
    using InputMode = hhbui::modern::InputMode_HHBUI;
    using TextSelection = hhbui::modern::TextSelection_HHBUI;
    using ValidationResult = hhbui::modern::ValidationResult_HHBUI;
    using TextValidator = hhbui::modern::TextValidator_HHBUI;
}
