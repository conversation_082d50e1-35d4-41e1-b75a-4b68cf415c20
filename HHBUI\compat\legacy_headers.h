/**
** =====================================================================================
**
**       文件名称: legacy_headers.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】向后兼容性头文件 - 兼容性支持 （声明文件）
**
**       主要功能:
**       - 提供完整的向后兼容性支持
**       - 保持旧版本API接口不变
**       - 智能类型别名与函数映射
**       - 异常安全的兼容性包装
**       - 渐进式迁移支持机制
**       - 兼容性警告与建议系统
**       - 版本检测与特性支持
**
**       技术特性:
**       - 采用现代C++17标准与向后兼容
**       - 智能类型别名与函数重定向
**       - 异常安全保证与错误恢复机制
**       - 编译时兼容性检查优化
**       - 模板元编程与类型推导
**       - 标准库兼容性适配
**       - 跨版本API统一抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建向后兼容性支持系统
**                             2. 实现完整API兼容性映射
**                             3. 添加智能类型别名机制
**                             4. 集成异常安全保证机制
**                             5. 优化兼容性性能开销
**                             6. 确保渐进式迁移支持
**                             7. 添加兼容性调试支持
**
** =====================================================================================
**/

#pragma once

// 包含原始头文件以确保完整兼容性
#include "../application/define.h"
#include "../application/config.h"
#include "../common/vstring.hpp"
#include "../common/assist.h"
#include "../common/coordinate.h"
#include "../common/ziparchive.h"
#include "../engine/base.h"
#include "../engine/engine.h"
#include "../engine/matrix.h"
#include "../engine/animation.h"
#include "../engine/renderd2d.h"
#include "../engine/render_api.h"
#include "../engine/dx11_shader.h"
#include "../engine/dx11_buffer.h"
#include "../engine/dx11_render_manager.h"
#include "../engine/render_profiler.h"
#include "../engine/gdi_plus_integration.h"
#include "../element/resource.h"
#include "../element/color.h"
#include "../element/image.h"
#include "../element/font.h"
#include "../element/hook.h"
#include "../element/path.h"
#include "../element/brush.h"
#include "../element/region.h"
#include "../element/array.h"
#include "../element/layout.h"
#include "../element/wnd.h"
#include "../element/canvas.h"
#include "../element/control.h"
#include "../element/scroll.h"
#include "../element/menu.h"
#include "../element/listview.h"
#include "../element/droptarget.h"
#include "../control/combutton.h"
#include "../control/button.h"
#include "../control/static.h"
#include "../control/edit.h"
#include "../control/page.h"
#include "../control/item.h"
#include "../control/list.h"
#include "../control/badge.h"
#include "../control/check.h"
#include "../control/tabs.h"
#include "../control/progress.h"
#include "../control/slider.h"
#include "../control/groupbox.h"
#include "../control/hotkey.h"
#include "../control/imagebox.h"
#include "../control/colorpicker.h"
#include "../control/combobox.h"
#include "../control/treeview.h"
#include "../control/table.h"
#include "../control/loading.h"
#include "../control/wrappanel.h"
#include "../control/knobs.h"
#include "../control/datebox.h"
#include "../control/waveringview.h"
#include "../control/tour.h"
#include "../control/miniblink.h"
#include "../control/chart.h"
#include "../control/timeline.h"
#include "../control/segmented.h"
#include "../control/splashscreen.h"
#include "../control/login.h"
#include "../common/auto_ptr.hpp"

namespace hhbui::compat {

    /// 兼容性版本信息
    constexpr std::string_view HHBUI_LEGACY_VERSION = "1.0.0.0";
    constexpr std::string_view HHBUI_MODERN_VERSION = "2.0.0.0";

    /// 兼容性警告级别
    enum class CompatibilityWarningLevel_HHBUI : uint32_t {
        None = 0,
        Info = 1,
        Warning = 2,
        Deprecated = 3,
        Error = 4
    };

    /// 兼容性检查结果
    struct CompatibilityCheckResult_HHBUI {
        bool isCompatible = true;
        CompatibilityWarningLevel_HHBUI warningLevel = CompatibilityWarningLevel_HHBUI::None;
        std::string message;
        std::string suggestion;
        std::string modernAlternative;
    };

    /// 兼容性管理器
    class CompatibilityManager_HHBUI {
    public:
        /// 获取单例实例
        [[nodiscard]] static CompatibilityManager_HHBUI& getInstance_HHBUI() noexcept;

        /// 检查API兼容性
        [[nodiscard]] CompatibilityCheckResult_HHBUI checkApiCompatibility_HHBUI(
            std::string_view apiName,
            std::string_view version = HHBUI_LEGACY_VERSION
        ) const noexcept;

        /// 注册兼容性警告
        void registerWarning_HHBUI(
            std::string_view apiName,
            CompatibilityWarningLevel_HHBUI level,
            std::string_view message,
            std::string_view suggestion = {},
            std::string_view modernAlternative = {}
        ) noexcept;

        /// 启用/禁用兼容性警告
        void setWarningsEnabled_HHBUI(bool enabled) noexcept;

        /// 检查兼容性警告是否启用
        [[nodiscard]] bool areWarningsEnabled_HHBUI() const noexcept;

        /// 设置警告级别阈值
        void setWarningThreshold_HHBUI(CompatibilityWarningLevel_HHBUI threshold) noexcept;

        /// 获取警告级别阈值
        [[nodiscard]] CompatibilityWarningLevel_HHBUI getWarningThreshold_HHBUI() const noexcept;

        /// 输出兼容性报告
        [[nodiscard]] std::string generateCompatibilityReport_HHBUI() const noexcept;

    private:
        CompatibilityManager_HHBUI() = default;
        
        struct CompatibilityInfo_HHBUI {
            CompatibilityWarningLevel_HHBUI level;
            std::string message;
            std::string suggestion;
            std::string modernAlternative;
            size_t usageCount = 0;
        };

        std::unordered_map<std::string, CompatibilityInfo_HHBUI> compatibilityMap_{};
        bool warningsEnabled_ = true;
        CompatibilityWarningLevel_HHBUI warningThreshold_ = CompatibilityWarningLevel_HHBUI::Warning;
        mutable std::shared_mutex mutex_{};
    };

    /// 兼容性警告宏
    #define HHBUI_COMPAT_WARNING(apiName, level, message, suggestion, modernAlternative) \
        do { \
            static bool warned = false; \
            if (!warned) { \
                hhbui::compat::CompatibilityManager_HHBUI::getInstance_HHBUI().registerWarning_HHBUI( \
                    apiName, level, message, suggestion, modernAlternative \
                ); \
                warned = true; \
            } \
        } while(0)

    #define HHBUI_DEPRECATED(apiName, modernAlternative) \
        HHBUI_COMPAT_WARNING(apiName, hhbui::compat::CompatibilityWarningLevel_HHBUI::Deprecated, \
                             "This API is deprecated and will be removed in future versions", \
                             "Please migrate to the modern API", modernAlternative)

    /// 兼容性包装器模板
    template<typename LegacyType, typename ModernType>
    class CompatibilityWrapper_HHBUI {
    public:
        /// 构造函数
        explicit CompatibilityWrapper_HHBUI(ModernType&& modern) noexcept
            : modern_(std::move(modern)) {}

        /// 转换为旧类型
        operator LegacyType() const noexcept {
            return convertToLegacy_HHBUI();
        }

        /// 转换为现代类型
        operator ModernType&() noexcept {
            return modern_;
        }

        /// 转换为现代类型（const）
        operator const ModernType&() const noexcept {
            return modern_;
        }

        /// 获取现代对象
        [[nodiscard]] ModernType& getModern_HHBUI() noexcept {
            return modern_;
        }

        /// 获取现代对象（const）
        [[nodiscard]] const ModernType& getModern_HHBUI() const noexcept {
            return modern_;
        }

    private:
        /// 转换为旧类型的实现（需要特化）
        [[nodiscard]] LegacyType convertToLegacy_HHBUI() const noexcept;

        ModernType modern_;
    };

    /// 兼容性函数包装器
    template<typename ReturnType, typename... Args>
    class CompatibilityFunctionWrapper_HHBUI {
    public:
        using LegacyFunction = std::function<ReturnType(Args...)>;
        using ModernFunction = std::function<ReturnType(Args...)>;

        /// 构造函数
        CompatibilityFunctionWrapper_HHBUI(
            LegacyFunction legacyFunc,
            ModernFunction modernFunc,
            std::string_view apiName
        ) noexcept
            : legacyFunc_(legacyFunc)
            , modernFunc_(modernFunc)
            , apiName_(apiName) {}

        /// 调用函数
        ReturnType operator()(Args... args) const {
            // 检查是否应该使用现代版本
            if (shouldUseModern_HHBUI()) {
                return modernFunc_(std::forward<Args>(args)...);
            } else {
                // 发出兼容性警告
                HHBUI_COMPAT_WARNING(
                    apiName_.c_str(),
                    CompatibilityWarningLevel_HHBUI::Warning,
                    "Using legacy API implementation",
                    "Consider migrating to modern API for better performance",
                    "Use the modern equivalent function"
                );
                return legacyFunc_(std::forward<Args>(args)...);
            }
        }

    private:
        /// 检查是否应该使用现代版本
        [[nodiscard]] bool shouldUseModern_HHBUI() const noexcept {
            // 这里可以添加逻辑来决定使用哪个版本
            // 例如基于配置、性能测试结果等
            return false; // 默认使用旧版本以确保兼容性
        }

        LegacyFunction legacyFunc_;
        ModernFunction modernFunc_;
        std::string apiName_;
    };

    /// 兼容性类型映射
    namespace type_mapping {
        
        /// 字符串类型映射
        using LegacyString = std::wstring;
        using ModernString = hhbui::modern::String;

        /// 智能指针类型映射
        template<typename T>
        using LegacyAutoPtr = HHBUI::ExAutoPtr<T>;
        
        template<typename T>
        using ModernUniquePtr = hhbui::modern::UniquePtr<T>;
        
        template<typename T>
        using ModernSharedPtr = hhbui::modern::SharedPtr<T>;

        /// 容器类型映射
        template<typename T>
        using LegacyVector = std::vector<T>;
        
        template<typename T>
        using ModernVector = hhbui::modern::Vector<T>;

        /// 函数类型映射
        template<typename Signature>
        using LegacyFunction = std::function<Signature>;
        
        template<typename Signature>
        using ModernFunction = hhbui::modern::Function<Signature>;

    } // namespace type_mapping

    /// 兼容性转换函数

    /// 字符串转换
    [[nodiscard]] inline type_mapping::ModernString toModern_HHBUI(const type_mapping::LegacyString& legacy) noexcept {
        return type_mapping::ModernString(legacy);
    }

    [[nodiscard]] inline type_mapping::LegacyString toLegacy_HHBUI(const type_mapping::ModernString& modern) noexcept {
        return type_mapping::LegacyString(modern);
    }

    /// 智能指针转换
    template<typename T>
    [[nodiscard]] type_mapping::ModernUniquePtr<T> toModern_HHBUI(type_mapping::LegacyAutoPtr<T>&& legacy) noexcept {
        T* ptr = legacy.Detach();
        return type_mapping::ModernUniquePtr<T>(ptr);
    }

    template<typename T>
    [[nodiscard]] type_mapping::LegacyAutoPtr<T> toLegacy_HHBUI(type_mapping::ModernUniquePtr<T>&& modern) noexcept {
        T* ptr = modern.release();
        return type_mapping::LegacyAutoPtr<T>(ptr, false);
    }

    /// 容器转换
    template<typename T>
    [[nodiscard]] type_mapping::ModernVector<T> toModern_HHBUI(const type_mapping::LegacyVector<T>& legacy) noexcept {
        return type_mapping::ModernVector<T>(legacy.begin(), legacy.end());
    }

    template<typename T>
    [[nodiscard]] type_mapping::LegacyVector<T> toLegacy_HHBUI(const type_mapping::ModernVector<T>& modern) noexcept {
        return type_mapping::LegacyVector<T>(modern.begin(), modern.end());
    }

    /// 兼容性检查函数
    [[nodiscard]] inline bool isLegacyApiAvailable_HHBUI(std::string_view apiName) noexcept {
        return CompatibilityManager_HHBUI::getInstance_HHBUI()
            .checkApiCompatibility_HHBUI(apiName).isCompatible;
    }

    [[nodiscard]] inline bool isModernApiRecommended_HHBUI(std::string_view apiName) noexcept {
        auto result = CompatibilityManager_HHBUI::getInstance_HHBUI()
            .checkApiCompatibility_HHBUI(apiName);
        return result.warningLevel >= CompatibilityWarningLevel_HHBUI::Warning;
    }

} // namespace hhbui::compat

// 全局兼容性别名（在全局命名空间中）
namespace HHBUI {
    
    /// 兼容性管理器别名
    using CompatibilityManager = hhbui::compat::CompatibilityManager_HHBUI;
    
    /// 兼容性检查结果别名
    using CompatibilityCheckResult = hhbui::compat::CompatibilityCheckResult_HHBUI;
    
    /// 兼容性警告级别别名
    using CompatibilityWarningLevel = hhbui::compat::CompatibilityWarningLevel_HHBUI;

    /// 兼容性转换函数别名
    template<typename T>
    auto toModern(T&& legacy) noexcept -> decltype(hhbui::compat::toModern_HHBUI(std::forward<T>(legacy))) {
        return hhbui::compat::toModern_HHBUI(std::forward<T>(legacy));
    }

    template<typename T>
    auto toLegacy(T&& modern) noexcept -> decltype(hhbui::compat::toLegacy_HHBUI(std::forward<T>(modern))) {
        return hhbui::compat::toLegacy_HHBUI(std::forward<T>(modern));
    }

} // namespace HHBUI

/// 兼容性宏定义
#define HHBUI_ENABLE_LEGACY_SUPPORT 1
#define HHBUI_ENABLE_MODERN_FEATURES 1

#if HHBUI_ENABLE_LEGACY_SUPPORT
    #define HHBUI_LEGACY_API_AVAILABLE(apiName) \
        hhbui::compat::isLegacyApiAvailable_HHBUI(apiName)
#else
    #define HHBUI_LEGACY_API_AVAILABLE(apiName) false
#endif

#if HHBUI_ENABLE_MODERN_FEATURES
    #define HHBUI_MODERN_API_RECOMMENDED(apiName) \
        hhbui::compat::isModernApiRecommended_HHBUI(apiName)
#else
    #define HHBUI_MODERN_API_RECOMMENDED(apiName) false
#endif
