/*
** =====================================================================================
**
**       文件名称: hhbui_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化C++17 UI框架 - 主头文件 （声明文件）
**
**       主要功能:
**       - 现代化C++17标准UI框架
**       - 高性能DirectX11/D2D渲染引擎
**       - 完整的UI控件库与布局系统
**       - 跨平台兼容性与向后兼容
**       - 智能内存管理与线程安全
**       - 异常安全保证与错误恢复
**       - 高级动画与特效系统
**
**       技术特性:
**       - 采用现代C++17标准与最佳实践
**       - LLVM命名规范与驼峰命名法
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能多线程渲染管线
**       - 模板元编程与编译时优化
**       - 标准库容器与算法集成
**
**       版权所有 (c) 2025 HHBUI：https://hhbui.com/
**       保留所有权利。
**       感谢 LIB：SOUI，EXDUI，ZLIB，PUGIXML...
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化C++17 UI框架
**                             2. 实现LLVM命名规范统一化
**                             3. 添加智能指针与RAII管理
**                             4. 集成异常安全保证机制
**                             5. 优化多线程渲染性能
**                             6. 确保完全向后兼容性
**                             7. 添加高级动画特效系统
**
** =====================================================================================
**/

#pragma once

// C++17标准库头文件
#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <array>
#include <unordered_map>
#include <unordered_set>
#include <algorithm>
#include <functional>
#include <optional>
#include <variant>
#include <any>
#include <chrono>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <atomic>
#include <future>
#include <execution>
#include <type_traits>
#include <utility>
#include <tuple>
#include <filesystem>

// 系统头文件
#include <inttypes.h>
#include <tchar.h>
#include <stdarg.h>
#include <stdint.h>
#include <limits.h>
#define NOMINMAX
#include <Windows.h>
#include <WindowsX.h>

// 现代化HHBUI命名空间
namespace hhbui::modern {

    // 版本信息
    constexpr std::string_view HHBUI_VERSION_MODERN = "2.0.0.0";
    constexpr uint32_t HHBUI_VERSION_MAJOR = 2;
    constexpr uint32_t HHBUI_VERSION_MINOR = 0;
    constexpr uint32_t HHBUI_VERSION_PATCH = 0;
    constexpr uint32_t HHBUI_VERSION_BUILD = 0;

    // 现代化类型别名
    using String = std::wstring;
    using StringView = std::wstring_view;
    using StringU8 = std::string;
    using StringViewU8 = std::string_view;
    
    template<typename T>
    using UniquePtr = std::unique_ptr<T>;
    
    template<typename T>
    using SharedPtr = std::shared_ptr<T>;
    
    template<typename T>
    using WeakPtr = std::weak_ptr<T>;
    
    template<typename T>
    using Optional = std::optional<T>;
    
    template<typename... Types>
    using Variant = std::variant<Types...>;
    
    using Any = std::any;
    
    template<typename T>
    using Vector = std::vector<T>;
    
    template<typename T, size_t N>
    using Array = std::array<T, N>;
    
    template<typename Key, typename Value>
    using HashMap = std::unordered_map<Key, Value>;
    
    template<typename T>
    using HashSet = std::unordered_set<T>;

    // 现代化函数类型别名
    template<typename Signature>
    using Function = std::function<Signature>;
    
    using ClsProc_HHBUI = Function<LRESULT(HWND, INT, WPARAM, LPARAM)>;
    using MsgProc_HHBUI = Function<LRESULT(HWND, LPVOID, LPVOID, INT, INT, WPARAM, LPARAM)>;
    using EventHandlerProc_HHBUI = Function<LRESULT(LPVOID, LPVOID, INT, INT, WPARAM, LPARAM)>;
    using ArrayCompareProc_HHBUI = Function<void(LPVOID, BOOL, size_t, size_t)>;
    using AnimationProc_HHBUI = Function<void(LPVOID, INT, BOOL, BOOL, double, double, double, LONG_PTR, LONG_PTR, LONG_PTR, LONG_PTR)>;
    using LayoutProc_HHBUI = Function<LRESULT(INT, WPARAM, LPARAM)>;
    using EnumFileCallback_HHBUI = Function<BOOL(LPCWSTR, LPARAM)>;

    // 现代化资源类型别名
    using ImageHandle_HHBUI = LPVOID;
    using BrushHandle_HHBUI = LPVOID;
    using ZipHandle_HHBUI = LPVOID;
    using WindowView_HHBUI = INT;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIimage = hhbui::modern::ImageHandle_HHBUI;
    using UIbrush = hhbui::modern::BrushHandle_HHBUI;
    using UIzip = hhbui::modern::ZipHandle_HHBUI;
    using WndView = hhbui::modern::WindowView_HHBUI;
    using ClsPROC = hhbui::modern::ClsProc_HHBUI;
    using MsgPROC = hhbui::modern::MsgProc_HHBUI;
    using EventHandlerPROC = hhbui::modern::EventHandlerProc_HHBUI;
    using ArrayComparePROC = hhbui::modern::ArrayCompareProc_HHBUI;
    using AnimationPROC = hhbui::modern::AnimationProc_HHBUI;
    using LayoutPROC = hhbui::modern::LayoutProc_HHBUI;
    using EnumFileCallback = hhbui::modern::EnumFileCallback_HHBUI;
}

// 现代化核心模块包含
#include "core_modern/application_modern.h"
#include "core_modern/config_modern.h"
#include "core_modern/exception_modern.h"
#include "utils_modern/string_modern.h"
#include "utils_modern/memory_modern.h"
#include "utils_modern/threading_modern.h"
#include "utils_modern/performance_modern.h"
#include "utils_modern/logging_modern.h"
#include "graphics_modern/engine_modern.h"
#include "graphics_modern/render_api_modern.h"
#include "ui_modern/control_base_modern.h"
#include "ui_modern/window_modern.h"
#include "control_modern/button_modern.h"
#include "control_modern/edit_modern.h"

// 向后兼容性包含
#include "compat/legacy_headers.h"

// 现代化HHBUI全局函数声明
namespace hhbui::modern {

    /// 初始化HHBUI现代化框架
    [[nodiscard]] bool initializeFramework_HHBUI(const ApplicationInitInfo_HHBUI& initInfo) noexcept;

    /// 关闭HHBUI现代化框架
    void shutdownFramework_HHBUI() noexcept;

    /// 检查框架是否已初始化
    [[nodiscard]] bool isFrameworkInitialized_HHBUI() noexcept;

    /// 获取框架版本信息
    [[nodiscard]] StringView getFrameworkVersion_HHBUI() noexcept;

    /// 运行应用程序主循环
    [[nodiscard]] int32_t runApplication_HHBUI() noexcept;

    /// 退出应用程序
    void exitApplication_HHBUI(int32_t exitCode = 0) noexcept;

} // namespace hhbui::modern

// 向后兼容性全局函数
namespace HHBUI {

    /// 初始化引擎（兼容性函数）
    inline HRESULT Init(info_Init* info = nullptr) {
        hhbui::modern::ApplicationInitInfo_HHBUI modernInfo{};
        if (info) {
            modernInfo.instanceHandle = info->hInstance;
            modernInfo.dpiScale = info->dwScaledpi;
            modernInfo.debugMode = info->dwDebug;
            modernInfo.defaultFontFace = info->default_font_Face ? info->default_font_Face : L"Segoe UI";
            modernInfo.defaultFontSize = info->default_font_Size;
            modernInfo.defaultFontStyle = info->default_font_Style;
        }

        return hhbui::modern::initializeFramework_HHBUI(modernInfo) ? S_OK : E_FAIL;
    }

    /// 反初始化引擎（兼容性函数）
    inline HRESULT UnInit() {
        hhbui::modern::shutdownFramework_HHBUI();
        return S_OK;
    }

    /// 查询是否已初始化（兼容性函数）
    inline BOOL QueryInit() {
        return hhbui::modern::isFrameworkInitialized_HHBUI() ? TRUE : FALSE;
    }

    /// 获取版本信息（兼容性函数）
    inline LPCWSTR GetVersion() {
        static std::wstring version = hhbui::modern::StringUtils_HHBUI::utf8ToUtf16_HHBUI(
            std::string(hhbui::modern::getFrameworkVersion_HHBUI())
        ).value_or(L"2.0.0.0");
        return version.c_str();
    }

} // namespace HHBUI
