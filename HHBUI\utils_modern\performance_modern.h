/**
** =====================================================================================
**
**       文件名称: performance_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化性能监控系统 - 性能监控 （声明文件）
**
**       主要功能:
**       - 现代化性能监控与分析系统
**       - 高精度时间测量与性能统计
**       - 智能内存使用监控与分析
**       - 异常安全的性能测量接口
**       - 跨平台性能监控抽象层
**       - 性能数据可视化与报告
**       - 现代化性能优化建议系统
**
**       技术特性:
**       - 采用现代C++17标准与高精度计时
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能无锁数据结构优化
**       - 模板元编程与编译时优化
**       - 标准库计时与统计算法集成
**       - 跨平台性能监控抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化性能监控系统
**                             2. 实现高精度时间测量机制
**                             3. 添加智能内存监控系统
**                             4. 集成异常安全保证机制
**                             5. 优化性能监控开销
**                             6. 确保跨平台兼容性
**                             7. 添加性能分析报告
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <chrono>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <vector>
#include <queue>
#include <functional>
#include <optional>
#include <thread>

namespace hhbui::modern {

    // 前向声明
    class PerformanceProfiler_HHBUI;
    class MemoryProfiler_HHBUI;
    class ThreadProfiler_HHBUI;

    /// 性能计数器类型枚举
    enum class PerformanceCounterType_HHBUI : uint32_t {
        Timer = 0,          // 时间计数器
        Counter = 1,        // 普通计数器
        Memory = 2,         // 内存计数器
        Throughput = 3,     // 吞吐量计数器
        Latency = 4,        // 延迟计数器
        Custom = 5          // 自定义计数器
    };

    /// 性能数据单位枚举
    enum class PerformanceUnit_HHBUI : uint32_t {
        None = 0,
        Nanoseconds = 1,
        Microseconds = 2,
        Milliseconds = 3,
        Seconds = 4,
        Bytes = 5,
        Kilobytes = 6,
        Megabytes = 7,
        Gigabytes = 8,
        Count = 9,
        Percentage = 10,
        Rate = 11
    };

    /// 性能统计信息
    struct PerformanceStats_HHBUI {
        double minimum = std::numeric_limits<double>::max();
        double maximum = std::numeric_limits<double>::lowest();
        double average = 0.0;
        double total = 0.0;
        uint64_t count = 0;
        double variance = 0.0;
        double standardDeviation = 0.0;
        std::chrono::steady_clock::time_point firstSample;
        std::chrono::steady_clock::time_point lastSample;
    };

    /// 性能计数器类
    class PerformanceCounter_HHBUI {
    public:
        /// 构造函数
        explicit PerformanceCounter_HHBUI(
            StringView name,
            PerformanceCounterType_HHBUI type = PerformanceCounterType_HHBUI::Counter,
            PerformanceUnit_HHBUI unit = PerformanceUnit_HHBUI::None
        ) noexcept;

        /// 析构函数
        ~PerformanceCounter_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        PerformanceCounter_HHBUI(const PerformanceCounter_HHBUI&) = delete;
        PerformanceCounter_HHBUI& operator=(const PerformanceCounter_HHBUI&) = delete;

        /// 启用移动构造和赋值
        PerformanceCounter_HHBUI(PerformanceCounter_HHBUI&&) noexcept = default;
        PerformanceCounter_HHBUI& operator=(PerformanceCounter_HHBUI&&) noexcept = default;

        /// 添加样本数据
        void addSample_HHBUI(double value) noexcept;

        /// 增加计数
        void increment_HHBUI(double delta = 1.0) noexcept;

        /// 减少计数
        void decrement_HHBUI(double delta = 1.0) noexcept;

        /// 设置当前值
        void setValue_HHBUI(double value) noexcept;

        /// 获取当前值
        [[nodiscard]] double getValue_HHBUI() const noexcept;

        /// 重置计数器
        void reset_HHBUI() noexcept;

        /// 获取统计信息
        [[nodiscard]] PerformanceStats_HHBUI getStats_HHBUI() const noexcept;

        /// 获取计数器名称
        [[nodiscard]] const String& getName_HHBUI() const noexcept;

        /// 获取计数器类型
        [[nodiscard]] PerformanceCounterType_HHBUI getType_HHBUI() const noexcept;

        /// 获取数据单位
        [[nodiscard]] PerformanceUnit_HHBUI getUnit_HHBUI() const noexcept;

        /// 获取样本数量
        [[nodiscard]] uint64_t getSampleCount_HHBUI() const noexcept;

        /// 检查是否有数据
        [[nodiscard]] bool hasData_HHBUI() const noexcept;

    private:
        /// 更新统计信息
        void updateStats_HHBUI(double value) noexcept;

    private:
        String name_;
        PerformanceCounterType_HHBUI type_;
        PerformanceUnit_HHBUI unit_;
        
        mutable std::shared_mutex mutex_;
        std::atomic<double> currentValue_{0.0};
        PerformanceStats_HHBUI stats_{};
        
        Vector<double> samples_{};
        size_t maxSamples_ = 1000;
        bool enableSampleHistory_ = true;
    };

    /// 高精度计时器类
    class HighResolutionTimer_HHBUI {
    public:
        /// 构造函数
        HighResolutionTimer_HHBUI() noexcept;

        /// 析构函数
        ~HighResolutionTimer_HHBUI() noexcept = default;

        /// 开始计时
        void start_HHBUI() noexcept;

        /// 停止计时
        void stop_HHBUI() noexcept;

        /// 重置计时器
        void reset_HHBUI() noexcept;

        /// 获取经过的时间（纳秒）
        [[nodiscard]] uint64_t getElapsedNanoseconds_HHBUI() const noexcept;

        /// 获取经过的时间（微秒）
        [[nodiscard]] uint64_t getElapsedMicroseconds_HHBUI() const noexcept;

        /// 获取经过的时间（毫秒）
        [[nodiscard]] uint64_t getElapsedMilliseconds_HHBUI() const noexcept;

        /// 获取经过的时间（秒）
        [[nodiscard]] double getElapsedSeconds_HHBUI() const noexcept;

        /// 检查计时器是否正在运行
        [[nodiscard]] bool isRunning_HHBUI() const noexcept;

        /// 获取当前高精度时间戳
        [[nodiscard]] static uint64_t getCurrentTimestamp_HHBUI() noexcept;

        /// 获取计时器分辨率（纳秒）
        [[nodiscard]] static uint64_t getResolution_HHBUI() noexcept;

    private:
        std::chrono::steady_clock::time_point startTime_{};
        std::chrono::steady_clock::time_point endTime_{};
        std::atomic<bool> running_{false};
    };

    /// 性能作用域计时器（RAII）
    class ScopedTimer_HHBUI {
    public:
        /// 构造函数
        explicit ScopedTimer_HHBUI(
            PerformanceCounter_HHBUI* counter,
            StringView description = L""
        ) noexcept;

        /// 构造函数（带回调）
        explicit ScopedTimer_HHBUI(
            Function<void(double)> callback,
            StringView description = L""
        ) noexcept;

        /// 析构函数
        ~ScopedTimer_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        ScopedTimer_HHBUI(const ScopedTimer_HHBUI&) = delete;
        ScopedTimer_HHBUI& operator=(const ScopedTimer_HHBUI&) = delete;

        /// 禁用移动构造和赋值
        ScopedTimer_HHBUI(ScopedTimer_HHBUI&&) = delete;
        ScopedTimer_HHBUI& operator=(ScopedTimer_HHBUI&&) = delete;

        /// 获取当前经过的时间
        [[nodiscard]] double getElapsedTime_HHBUI() const noexcept;

    private:
        HighResolutionTimer_HHBUI timer_;
        PerformanceCounter_HHBUI* counter_ = nullptr;
        Function<void(double)> callback_{};
        String description_;
    };

    /// 性能监控管理器类
    class PerformanceMonitor_HHBUI {
    public:
        /// 构造函数
        PerformanceMonitor_HHBUI() noexcept;

        /// 析构函数
        ~PerformanceMonitor_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        PerformanceMonitor_HHBUI(const PerformanceMonitor_HHBUI&) = delete;
        PerformanceMonitor_HHBUI& operator=(const PerformanceMonitor_HHBUI&) = delete;

        /// 创建性能计数器
        [[nodiscard]] PerformanceCounter_HHBUI* createCounter_HHBUI(
            StringView name,
            PerformanceCounterType_HHBUI type = PerformanceCounterType_HHBUI::Counter,
            PerformanceUnit_HHBUI unit = PerformanceUnit_HHBUI::None
        ) noexcept;

        /// 获取性能计数器
        [[nodiscard]] PerformanceCounter_HHBUI* getCounter_HHBUI(StringView name) const noexcept;

        /// 删除性能计数器
        void removeCounter_HHBUI(StringView name) noexcept;

        /// 获取所有计数器名称
        [[nodiscard]] Vector<String> getCounterNames_HHBUI() const noexcept;

        /// 重置所有计数器
        void resetAllCounters_HHBUI() noexcept;

        /// 启用/禁用性能监控
        void setEnabled_HHBUI(bool enabled) noexcept;

        /// 检查性能监控是否启用
        [[nodiscard]] bool isEnabled_HHBUI() const noexcept;

        /// 设置采样间隔
        void setSamplingInterval_HHBUI(std::chrono::milliseconds interval) noexcept;

        /// 获取采样间隔
        [[nodiscard]] std::chrono::milliseconds getSamplingInterval_HHBUI() const noexcept;

        /// 开始性能监控
        void startMonitoring_HHBUI() noexcept;

        /// 停止性能监控
        void stopMonitoring_HHBUI() noexcept;

        /// 生成性能报告
        [[nodiscard]] String generateReport_HHBUI() const noexcept;

        /// 导出性能数据
        [[nodiscard]] bool exportData_HHBUI(StringView filePath) const noexcept;

        /// 获取系统性能信息
        struct SystemPerformance_HHBUI {
            double cpuUsage = 0.0;
            uint64_t memoryUsed = 0;
            uint64_t memoryAvailable = 0;
            uint32_t threadCount = 0;
            uint32_t handleCount = 0;
            double diskUsage = 0.0;
            double networkUsage = 0.0;
        };
        [[nodiscard]] SystemPerformance_HHBUI getSystemPerformance_HHBUI() const noexcept;

        /// 获取单例实例
        [[nodiscard]] static PerformanceMonitor_HHBUI& getInstance_HHBUI() noexcept;

    private:
        /// 监控线程函数
        void monitoringThread_HHBUI() noexcept;

        /// 更新系统性能信息
        void updateSystemPerformance_HHBUI() noexcept;

    private:
        mutable std::shared_mutex countersMutex_{};
        HashMap<String, UniquePtr<PerformanceCounter_HHBUI>> counters_{};
        
        std::atomic<bool> enabled_{true};
        std::atomic<bool> monitoring_{false};
        std::chrono::milliseconds samplingInterval_{100};
        
        UniquePtr<std::thread> monitoringThread_{};
        std::atomic<bool> shouldStopMonitoring_{false};
        
        SystemPerformance_HHBUI systemPerformance_{};
        mutable std::mutex systemPerfMutex_{};
        
        static inline PerformanceMonitor_HHBUI* instance_{nullptr};
        static inline std::once_flag instanceFlag_{};
    };

    /// 全局性能监控函数

    /// 获取性能监控器实例
    [[nodiscard]] PerformanceMonitor_HHBUI& getPerformanceMonitor_HHBUI() noexcept;

    /// 创建性能计数器
    [[nodiscard]] PerformanceCounter_HHBUI* createPerformanceCounter_HHBUI(
        StringView name,
        PerformanceCounterType_HHBUI type = PerformanceCounterType_HHBUI::Counter,
        PerformanceUnit_HHBUI unit = PerformanceUnit_HHBUI::None
    ) noexcept;

    /// 获取性能计数器
    [[nodiscard]] PerformanceCounter_HHBUI* getPerformanceCounter_HHBUI(StringView name) noexcept;

    /// 测量函数执行时间
    template<typename Func, typename... Args>
    [[nodiscard]] auto measureExecutionTime_HHBUI(Func&& func, Args&&... args) noexcept;

    /// 性能监控宏定义
    #define HHBUI_PERF_COUNTER(name, type, unit) \
        hhbui::modern::createPerformanceCounter_HHBUI(name, type, unit)

    #define HHBUI_PERF_TIMER(name) \
        hhbui::modern::ScopedTimer_HHBUI timer_##__LINE__(hhbui::modern::getPerformanceCounter_HHBUI(name), name)

    #define HHBUI_PERF_SCOPE(description) \
        hhbui::modern::ScopedTimer_HHBUI timer_##__LINE__([](double elapsed) { \
            /* 可以在这里添加日志记录 */ \
        }, description)

    #define HHBUI_PERF_MEASURE(func, ...) \
        hhbui::modern::measureExecutionTime_HHBUI(func, ##__VA_ARGS__)

    // ========== 模板实现 ==========

    template<typename Func, typename... Args>
    auto measureExecutionTime_HHBUI(Func&& func, Args&&... args) noexcept {
        HighResolutionTimer_HHBUI timer;
        timer.start_HHBUI();
        
        if constexpr (std::is_void_v<decltype(func(args...))>) {
            func(std::forward<Args>(args)...);
            timer.stop_HHBUI();
            return timer.getElapsedMicroseconds_HHBUI();
        } else {
            auto result = func(std::forward<Args>(args)...);
            timer.stop_HHBUI();
            return std::make_pair(result, timer.getElapsedMicroseconds_HHBUI());
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using PerformanceMonitor = hhbui::modern::PerformanceMonitor_HHBUI;
    using PerformanceCounter = hhbui::modern::PerformanceCounter_HHBUI;
    using HighResolutionTimer = hhbui::modern::HighResolutionTimer_HHBUI;
    using ScopedTimer = hhbui::modern::ScopedTimer_HHBUI;
}
