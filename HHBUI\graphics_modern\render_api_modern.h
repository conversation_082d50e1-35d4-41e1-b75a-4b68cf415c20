/**
** =====================================================================================
**
**       文件名称: render_api_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化渲染API接口 - 渲染API （声明文件）
**
**       主要功能:
**       - 现代化DirectX11/D2D渲染API接口
**       - 高性能GPU资源管理与缓存
**       - 智能着色器编译与管理系统
**       - 异常安全的渲染操作接口
**       - 跨平台渲染抽象与兼容
**       - 渲染性能监控与调试支持
**       - 现代化COM接口与引用计数
**
**       技术特性:
**       - 采用现代C++17标准与COM规范
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能GPU内存管理优化
**       - 模板元编程与编译时优化
**       - 多线程安全的渲染接口
**       - 跨平台渲染API抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化渲染API接口
**                             2. 实现智能资源管理机制
**                             3. 添加高性能渲染管线
**                             4. 集成异常安全保证机制
**                             5. 优化GPU资源管理
**                             6. 确保跨平台兼容性
**                             7. 添加渲染调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_map>
#include <optional>
#include <functional>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <guiddef.h>
#include <wrl/client.h>

namespace hhbui::modern {

    // 前向声明
    class Shader_HHBUI;
    class Buffer_HHBUI;
    class Texture_HHBUI;
    class RenderState_HHBUI;

    /// 渲染器类型枚举
    enum class RenderType_HHBUI : uint32_t {
        D2D_ONLY = 0,      // 仅使用D2D渲染
        D3D_ONLY = 1,      // 仅使用D3D渲染
        HYBRID = 2,        // 混合渲染模式
        GDI_COMPAT = 3     // GDI兼容模式
    };

    /// 着色器类型枚举
    enum class ShaderType_HHBUI : uint32_t {
        VERTEX = 0,
        PIXEL = 1,
        GEOMETRY = 2,
        HULL = 3,
        DOMAIN_SHADER = 4,
        COMPUTE = 5
    };

    /// 缓冲区类型枚举
    enum class BufferType_HHBUI : uint32_t {
        VERTEX = 0,
        INDEX = 1,
        CONSTANT = 2,
        STRUCTURED = 3,
        STAGING = 4
    };

    /// 纹理类型枚举
    enum class TextureType_HHBUI : uint32_t {
        TEXTURE_1D = 0,
        TEXTURE_2D = 1,
        TEXTURE_3D = 2,
        TEXTURE_CUBE = 3,
        TEXTURE_ARRAY = 4
    };

    /// 渲染统计信息
    struct RenderStats_HHBUI {
        uint32_t drawCalls = 0;           // 绘制调用次数
        uint32_t triangles = 0;            // 三角形数量
        uint32_t vertices = 0;             // 顶点数量
        uint64_t gpuMemoryUsed = 0;        // GPU内存使用量
        float frameTimeMs = 0.0f;          // 帧时间(毫秒)
        float gpuTimeMs = 0.0f;            // GPU时间(毫秒)
        uint32_t textureBinds = 0;         // 纹理绑定次数
        uint32_t shaderSwitches = 0;       // 着色器切换次数
        uint32_t renderStateChanges = 0;   // 渲染状态变更次数
    };

    /// 现代化渲染对象基类
    class RenderObject_HHBUI {
    public:
        /// 构造函数
        RenderObject_HHBUI() noexcept = default;

        /// 虚析构函数
        virtual ~RenderObject_HHBUI() noexcept = default;

        /// 禁用拷贝构造和赋值
        RenderObject_HHBUI(const RenderObject_HHBUI&) = delete;
        RenderObject_HHBUI& operator=(const RenderObject_HHBUI&) = delete;

        /// 启用移动构造和赋值
        RenderObject_HHBUI(RenderObject_HHBUI&&) noexcept = default;
        RenderObject_HHBUI& operator=(RenderObject_HHBUI&&) noexcept = default;

        /// 获取渲染器类型
        [[nodiscard]] virtual RenderType_HHBUI getRenderType_HHBUI() const noexcept = 0;

        /// 获取对象名称
        [[nodiscard]] virtual StringView getName_HHBUI() const noexcept = 0;

        /// 设置对象名称
        virtual void setName_HHBUI(StringView name) noexcept = 0;

        /// 检查对象是否有效
        [[nodiscard]] virtual bool isValid_HHBUI() const noexcept = 0;

        /// 获取对象大小（字节）
        [[nodiscard]] virtual size_t getSize_HHBUI() const noexcept = 0;

        /// 获取创建时间
        [[nodiscard]] virtual std::chrono::steady_clock::time_point getCreationTime_HHBUI() const noexcept = 0;

    protected:
        String name_{};
        std::chrono::steady_clock::time_point creationTime_{std::chrono::steady_clock::now()};
    };

    /// 现代化着色器类
    class Shader_HHBUI : public RenderObject_HHBUI {
    public:
        /// 构造函数
        explicit Shader_HHBUI(ShaderType_HHBUI type, StringView name = L"HHBUI_Shader") noexcept;

        /// 析构函数
        ~Shader_HHBUI() noexcept override;

        /// 编译着色器
        [[nodiscard]] bool compile_HHBUI(StringView sourceCode, StringView entryPoint, StringView target) noexcept;

        /// 从文件加载着色器
        [[nodiscard]] bool loadFromFile_HHBUI(StringView filePath, StringView entryPoint, StringView target) noexcept;

        /// 绑定着色器到渲染管线
        [[nodiscard]] bool bind_HHBUI() noexcept;

        /// 解绑着色器
        void unbind_HHBUI() noexcept;

        /// 获取着色器类型
        [[nodiscard]] ShaderType_HHBUI getShaderType_HHBUI() const noexcept;

        /// 设置常量缓冲区
        [[nodiscard]] bool setConstantBuffer_HHBUI(uint32_t slot, const Buffer_HHBUI* buffer) noexcept;

        /// 获取编译错误信息
        [[nodiscard]] const String& getCompileErrors_HHBUI() const noexcept;

        /// 检查是否已编译
        [[nodiscard]] bool isCompiled_HHBUI() const noexcept;

        /// 获取字节码
        [[nodiscard]] const Vector<uint8_t>& getBytecode_HHBUI() const noexcept;

        // RenderObject_HHBUI接口实现
        [[nodiscard]] RenderType_HHBUI getRenderType_HHBUI() const noexcept override;
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        void setName_HHBUI(StringView name) noexcept override;
        [[nodiscard]] bool isValid_HHBUI() const noexcept override;
        [[nodiscard]] size_t getSize_HHBUI() const noexcept override;
        [[nodiscard]] std::chrono::steady_clock::time_point getCreationTime_HHBUI() const noexcept override;

    private:
        /// 内部编译实现
        [[nodiscard]] bool internalCompile_HHBUI(StringView sourceCode, StringView entryPoint, StringView target) noexcept;

    private:
        ShaderType_HHBUI shaderType_;
        Microsoft::WRL::ComPtr<ID3D11VertexShader> vertexShader_{};
        Microsoft::WRL::ComPtr<ID3D11PixelShader> pixelShader_{};
        Microsoft::WRL::ComPtr<ID3D11GeometryShader> geometryShader_{};
        Microsoft::WRL::ComPtr<ID3D11HullShader> hullShader_{};
        Microsoft::WRL::ComPtr<ID3D11DomainShader> domainShader_{};
        Microsoft::WRL::ComPtr<ID3D11ComputeShader> computeShader_{};
        
        Vector<uint8_t> bytecode_{};
        String compileErrors_{};
        std::atomic<bool> compiled_{false};
    };

    /// 现代化缓冲区类
    class Buffer_HHBUI : public RenderObject_HHBUI {
    public:
        /// 构造函数
        explicit Buffer_HHBUI(BufferType_HHBUI type, StringView name = L"HHBUI_Buffer") noexcept;

        /// 析构函数
        ~Buffer_HHBUI() noexcept override;

        /// 创建缓冲区
        [[nodiscard]] bool create_HHBUI(uint32_t size, const void* initialData = nullptr,
                                        bool dynamic = false, bool cpuAccess = false) noexcept;

        /// 更新缓冲区数据
        [[nodiscard]] bool updateData_HHBUI(const void* data, uint32_t size, uint32_t offset = 0) noexcept;

        /// 映射缓冲区内存
        [[nodiscard]] void* map_HHBUI(bool readOnly = false) noexcept;

        /// 解除映射
        void unmap_HHBUI() noexcept;

        /// 绑定到渲染管线
        [[nodiscard]] bool bind_HHBUI(uint32_t slot) noexcept;

        /// 获取缓冲区大小
        [[nodiscard]] uint32_t getBufferSize_HHBUI() const noexcept;

        /// 获取缓冲区类型
        [[nodiscard]] BufferType_HHBUI getBufferType_HHBUI() const noexcept;

        /// 检查是否为动态缓冲区
        [[nodiscard]] bool isDynamic_HHBUI() const noexcept;

        /// 检查是否支持CPU访问
        [[nodiscard]] bool hasCpuAccess_HHBUI() const noexcept;

        // RenderObject_HHBUI接口实现
        [[nodiscard]] RenderType_HHBUI getRenderType_HHBUI() const noexcept override;
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        void setName_HHBUI(StringView name) noexcept override;
        [[nodiscard]] bool isValid_HHBUI() const noexcept override;
        [[nodiscard]] size_t getSize_HHBUI() const noexcept override;
        [[nodiscard]] std::chrono::steady_clock::time_point getCreationTime_HHBUI() const noexcept override;

    private:
        BufferType_HHBUI bufferType_;
        Microsoft::WRL::ComPtr<ID3D11Buffer> buffer_{};
        uint32_t bufferSize_ = 0;
        bool dynamic_ = false;
        bool cpuAccess_ = false;
        std::atomic<bool> mapped_{false};
    };

    /// 现代化纹理类
    class Texture_HHBUI : public RenderObject_HHBUI {
    public:
        /// 构造函数
        explicit Texture_HHBUI(TextureType_HHBUI type, StringView name = L"HHBUI_Texture") noexcept;

        /// 析构函数
        ~Texture_HHBUI() noexcept override;

        /// 创建2D纹理
        [[nodiscard]] bool create2D_HHBUI(uint32_t width, uint32_t height, DXGI_FORMAT format,
                                          const void* initialData = nullptr, bool renderTarget = false, 
                                          bool shaderResource = true) noexcept;

        /// 从文件加载纹理
        [[nodiscard]] bool loadFromFile_HHBUI(StringView filePath) noexcept;

        /// 绑定为着色器资源
        [[nodiscard]] bool bindAsShaderResource_HHBUI(uint32_t slot) noexcept;

        /// 绑定为渲染目标
        [[nodiscard]] bool bindAsRenderTarget_HHBUI() noexcept;

        /// 获取纹理尺寸
        [[nodiscard]] std::pair<uint32_t, uint32_t> getTextureSize_HHBUI() const noexcept;

        /// 获取纹理格式
        [[nodiscard]] DXGI_FORMAT getFormat_HHBUI() const noexcept;

        /// 获取纹理类型
        [[nodiscard]] TextureType_HHBUI getTextureType_HHBUI() const noexcept;

        /// 生成Mipmap
        void generateMipmaps_HHBUI() noexcept;

        // RenderObject_HHBUI接口实现
        [[nodiscard]] RenderType_HHBUI getRenderType_HHBUI() const noexcept override;
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        void setName_HHBUI(StringView name) noexcept override;
        [[nodiscard]] bool isValid_HHBUI() const noexcept override;
        [[nodiscard]] size_t getSize_HHBUI() const noexcept override;
        [[nodiscard]] std::chrono::steady_clock::time_point getCreationTime_HHBUI() const noexcept override;

    private:
        TextureType_HHBUI textureType_;
        Microsoft::WRL::ComPtr<ID3D11Texture2D> texture2D_{};
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> shaderResourceView_{};
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> renderTargetView_{};
        uint32_t width_ = 0;
        uint32_t height_ = 0;
        DXGI_FORMAT format_ = DXGI_FORMAT_UNKNOWN;
    };

    /// 全局渲染API函数

    /// 创建着色器
    [[nodiscard]] UniquePtr<Shader_HHBUI> createShader_HHBUI(ShaderType_HHBUI type, StringView name = L"") noexcept;

    /// 创建缓冲区
    [[nodiscard]] UniquePtr<Buffer_HHBUI> createBuffer_HHBUI(BufferType_HHBUI type, StringView name = L"") noexcept;

    /// 创建纹理
    [[nodiscard]] UniquePtr<Texture_HHBUI> createTexture_HHBUI(TextureType_HHBUI type, StringView name = L"") noexcept;

    /// 获取渲染统计信息
    [[nodiscard]] RenderStats_HHBUI getRenderStats_HHBUI() noexcept;

    /// 重置渲染统计信息
    void resetRenderStats_HHBUI() noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIShader = hhbui::modern::Shader_HHBUI;
    using UIBuffer = hhbui::modern::Buffer_HHBUI;
    using UITexture = hhbui::modern::Texture_HHBUI;
    using RenderStats = hhbui::modern::RenderStats_HHBUI;
}
