/**
** =====================================================================================
**
**       文件名称: exception_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化异常处理系统 - 异常管理 （声明文件）
**
**       主要功能:
**       - 现代化异常处理与错误恢复机制
**       - 高性能异常安全保证系统
**       - 智能错误诊断与调试信息
**       - 异常链追踪与错误传播
**       - 跨模块异常处理统一化
**       - 异常日志记录与分析
**       - 错误恢复策略与容错机制
**
**       技术特性:
**       - 采用现代C++17标准异常处理
**       - RAII异常安全保证与资源管理
**       - 智能异常链追踪与诊断
**       - 高性能错误码与异常混合机制
**       - 模板化异常处理与类型安全
**       - 标准库异常体系集成
**       - 跨平台异常处理抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化异常处理系统
**                             2. 实现RAII异常安全保证
**                             3. 添加智能异常链追踪
**                             4. 集成错误恢复机制
**                             5. 优化异常处理性能
**                             6. 确保跨平台兼容性
**                             7. 添加异常日志系统
**
** =====================================================================================
**/

#pragma once

#include <exception>
#include <stdexcept>
#include <system_error>
#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>
#include <source_location>
#include <stacktrace>
#include <chrono>
#include <functional>
#include <Windows.h>

namespace hhbui::modern {

    // 前向声明
    class ExceptionLogger_HHBUI;
    class ErrorRecovery_HHBUI;

    /// 错误级别枚举
    enum class ErrorLevel_HHBUI : uint32_t {
        Info = 0,
        Warning = 1,
        Error = 2,
        Critical = 3,
        Fatal = 4
    };

    /// 错误类别枚举
    enum class ErrorCategory_HHBUI : uint32_t {
        Unknown = 0,
        System = 1,
        Memory = 2,
        IO = 3,
        Network = 4,
        Graphics = 5,
        Audio = 6,
        UI = 7,
        Logic = 8,
        Validation = 9,
        Security = 10,
        Performance = 11
    };

    /// 错误恢复策略
    enum class RecoveryStrategy_HHBUI : uint32_t {
        None = 0,
        Retry = 1,
        Fallback = 2,
        Ignore = 3,
        Abort = 4,
        UserChoice = 5
    };

    /// 异常信息结构
    struct ExceptionInfo_HHBUI {
        String message;
        String details;
        ErrorLevel_HHBUI level = ErrorLevel_HHBUI::Error;
        ErrorCategory_HHBUI category = ErrorCategory_HHBUI::Unknown;
        uint32_t errorCode = 0;
        HRESULT hresult = S_OK;
        std::source_location location = std::source_location::current();
        std::stacktrace stackTrace;
        std::chrono::system_clock::time_point timestamp = std::chrono::system_clock::now();
        Optional<String> context;
        Optional<String> suggestion;
    };

    /// 基础异常类
    class Exception_HHBUI : public std::exception {
    public:
        /// 构造函数
        explicit Exception_HHBUI(
            StringView message,
            ErrorLevel_HHBUI level = ErrorLevel_HHBUI::Error,
            ErrorCategory_HHBUI category = ErrorCategory_HHBUI::Unknown,
            uint32_t errorCode = 0,
            std::source_location location = std::source_location::current()
        ) noexcept;

        /// 构造函数（带详细信息）
        Exception_HHBUI(
            const ExceptionInfo_HHBUI& info
        ) noexcept;

        /// 析构函数
        ~Exception_HHBUI() noexcept override = default;

        /// 获取异常消息
        [[nodiscard]] const char* what() const noexcept override;

        /// 获取异常信息
        [[nodiscard]] const ExceptionInfo_HHBUI& getInfo_HHBUI() const noexcept;

        /// 获取错误级别
        [[nodiscard]] ErrorLevel_HHBUI getLevel_HHBUI() const noexcept;

        /// 获取错误类别
        [[nodiscard]] ErrorCategory_HHBUI getCategory_HHBUI() const noexcept;

        /// 获取错误代码
        [[nodiscard]] uint32_t getErrorCode_HHBUI() const noexcept;

        /// 获取HRESULT
        [[nodiscard]] HRESULT getHResult_HHBUI() const noexcept;

        /// 获取源位置
        [[nodiscard]] const std::source_location& getLocation_HHBUI() const noexcept;

        /// 获取堆栈跟踪
        [[nodiscard]] const std::stacktrace& getStackTrace_HHBUI() const noexcept;

        /// 设置上下文信息
        void setContext_HHBUI(StringView context) noexcept;

        /// 设置建议信息
        void setSuggestion_HHBUI(StringView suggestion) noexcept;

        /// 格式化异常信息
        [[nodiscard]] String format_HHBUI() const noexcept;

    private:
        ExceptionInfo_HHBUI info_;
        mutable String formattedMessage_;
    };

    /// 系统异常类
    class SystemException_HHBUI : public Exception_HHBUI {
    public:
        explicit SystemException_HHBUI(
            StringView message,
            DWORD systemErrorCode = GetLastError(),
            std::source_location location = std::source_location::current()
        ) noexcept;
    };

    /// 内存异常类
    class MemoryException_HHBUI : public Exception_HHBUI {
    public:
        explicit MemoryException_HHBUI(
            StringView message,
            size_t requestedSize = 0,
            std::source_location location = std::source_location::current()
        ) noexcept;

        [[nodiscard]] size_t getRequestedSize_HHBUI() const noexcept;

    private:
        size_t requestedSize_;
    };

    /// IO异常类
    class IOException_HHBUI : public Exception_HHBUI {
    public:
        explicit IOException_HHBUI(
            StringView message,
            StringView filePath = {},
            std::source_location location = std::source_location::current()
        ) noexcept;

        [[nodiscard]] const String& getFilePath_HHBUI() const noexcept;

    private:
        String filePath_;
    };

    /// 图形异常类
    class GraphicsException_HHBUI : public Exception_HHBUI {
    public:
        explicit GraphicsException_HHBUI(
            StringView message,
            HRESULT hresult = E_FAIL,
            std::source_location location = std::source_location::current()
        ) noexcept;
    };

    /// UI异常类
    class UIException_HHBUI : public Exception_HHBUI {
    public:
        explicit UIException_HHBUI(
            StringView message,
            StringView controlName = {},
            std::source_location location = std::source_location::current()
        ) noexcept;

        [[nodiscard]] const String& getControlName_HHBUI() const noexcept;

    private:
        String controlName_;
    };

    /// 验证异常类
    class ValidationException_HHBUI : public Exception_HHBUI {
    public:
        explicit ValidationException_HHBUI(
            StringView message,
            StringView fieldName = {},
            StringView expectedValue = {},
            StringView actualValue = {},
            std::source_location location = std::source_location::current()
        ) noexcept;

        [[nodiscard]] const String& getFieldName_HHBUI() const noexcept;
        [[nodiscard]] const String& getExpectedValue_HHBUI() const noexcept;
        [[nodiscard]] const String& getActualValue_HHBUI() const noexcept;

    private:
        String fieldName_;
        String expectedValue_;
        String actualValue_;
    };

    /// 异常处理器类型
    using ExceptionHandler_HHBUI = Function<RecoveryStrategy_HHBUI(const Exception_HHBUI&)>;

    /// 异常管理器类
    class ExceptionManager_HHBUI {
    public:
        /// 构造函数
        ExceptionManager_HHBUI() noexcept;

        /// 析构函数
        ~ExceptionManager_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        ExceptionManager_HHBUI(const ExceptionManager_HHBUI&) = delete;
        ExceptionManager_HHBUI& operator=(const ExceptionManager_HHBUI&) = delete;

        /// 注册异常处理器
        void registerHandler_HHBUI(ErrorCategory_HHBUI category, ExceptionHandler_HHBUI handler) noexcept;

        /// 注销异常处理器
        void unregisterHandler_HHBUI(ErrorCategory_HHBUI category) noexcept;

        /// 处理异常
        [[nodiscard]] RecoveryStrategy_HHBUI handleException_HHBUI(const Exception_HHBUI& exception) noexcept;

        /// 记录异常
        void logException_HHBUI(const Exception_HHBUI& exception) noexcept;

        /// 设置异常日志记录器
        void setLogger_HHBUI(UniquePtr<ExceptionLogger_HHBUI> logger) noexcept;

        /// 启用/禁用异常日志
        void setLoggingEnabled_HHBUI(bool enabled) noexcept;

        /// 获取异常统计信息
        struct ExceptionStats_HHBUI {
            uint64_t totalExceptions = 0;
            HashMap<ErrorCategory_HHBUI, uint64_t> categoryCount;
            HashMap<ErrorLevel_HHBUI, uint64_t> levelCount;
            std::chrono::system_clock::time_point lastException;
        };
        [[nodiscard]] ExceptionStats_HHBUI getStats_HHBUI() const noexcept;

        /// 清除统计信息
        void clearStats_HHBUI() noexcept;

        /// 获取单例实例
        [[nodiscard]] static ExceptionManager_HHBUI& getInstance_HHBUI() noexcept;

    private:
        HashMap<ErrorCategory_HHBUI, ExceptionHandler_HHBUI> handlers_;
        UniquePtr<ExceptionLogger_HHBUI> logger_;
        bool loggingEnabled_ = true;
        ExceptionStats_HHBUI stats_;
        mutable std::mutex mutex_;
    };

    /// 异常安全包装器模板
    template<typename Func, typename... Args>
    [[nodiscard]] auto safeCall_HHBUI(Func&& func, Args&&... args) noexcept -> Optional<decltype(func(args...))> {
        try {
            if constexpr (std::is_void_v<decltype(func(args...))>) {
                func(std::forward<Args>(args)...);
                return {};
            } else {
                return func(std::forward<Args>(args)...);
            }
        } catch (const Exception_HHBUI& e) {
            ExceptionManager_HHBUI::getInstance_HHBUI().handleException_HHBUI(e);
            return std::nullopt;
        } catch (const std::exception& e) {
            Exception_HHBUI hhbuiException(
                String(e.what(), e.what() + strlen(e.what())),
                ErrorLevel_HHBUI::Error,
                ErrorCategory_HHBUI::Unknown
            );
            ExceptionManager_HHBUI::getInstance_HHBUI().handleException_HHBUI(hhbuiException);
            return std::nullopt;
        } catch (...) {
            Exception_HHBUI unknownException(
                L"Unknown exception occurred",
                ErrorLevel_HHBUI::Critical,
                ErrorCategory_HHBUI::Unknown
            );
            ExceptionManager_HHBUI::getInstance_HHBUI().handleException_HHBUI(unknownException);
            return std::nullopt;
        }
    }

    /// 宏定义
    #define HHBUI_THROW(ExceptionType, message) \
        throw ExceptionType(message, std::source_location::current())

    #define HHBUI_THROW_IF(condition, ExceptionType, message) \
        do { if (condition) HHBUI_THROW(ExceptionType, message); } while(0)

    #define HHBUI_SAFE_CALL(func, ...) \
        hhbui::modern::safeCall_HHBUI(func, ##__VA_ARGS__)

    #define HHBUI_TRY_CATCH(code, handler) \
        try { code; } catch (const hhbui::modern::Exception_HHBUI& e) { handler(e); }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using Exception = hhbui::modern::Exception_HHBUI;
    using SystemException = hhbui::modern::SystemException_HHBUI;
    using MemoryException = hhbui::modern::MemoryException_HHBUI;
    using IOException = hhbui::modern::IOException_HHBUI;
    using GraphicsException = hhbui::modern::GraphicsException_HHBUI;
    using UIException = hhbui::modern::UIException_HHBUI;
    using ValidationException = hhbui::modern::ValidationException_HHBUI;
}
