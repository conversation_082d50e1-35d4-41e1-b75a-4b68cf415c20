/**
** =====================================================================================
**
**       文件名称: config_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化配置管理系统 - 配置管理 （声明文件）
**
**       主要功能:
**       - 现代化配置文件管理与持久化
**       - 高性能JSON/XML配置解析
**       - 智能配置验证与类型安全
**       - 异常安全的配置读写操作
**       - 配置变更监听与通知机制
**       - 多格式配置文件支持
**       - 配置加密与安全存储
**
**       技术特性:
**       - 采用现代C++17标准与模板元编程
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能配置缓存与索引
**       - 类型安全的配置访问接口
**       - 标准库容器与算法集成
**       - 跨平台文件系统抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化配置管理系统
**                             2. 实现类型安全配置接口
**                             3. 添加配置变更监听机制
**                             4. 集成异常安全保证机制
**                             5. 优化配置解析性能
**                             6. 确保跨平台兼容性
**                             7. 添加配置加密支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <variant>
#include <optional>
#include <unordered_map>
#include <vector>
#include <functional>
#include <filesystem>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <type_traits>

namespace hhbui::modern {

    // 前向声明
    class ConfigValidator_HHBUI;
    class ConfigEncryption_HHBUI;

    /// 配置值类型
    using ConfigValue_HHBUI = Variant<
        bool,
        int32_t,
        int64_t,
        float,
        double,
        String,
        Vector<bool>,
        Vector<int32_t>,
        Vector<int64_t>,
        Vector<float>,
        Vector<double>,
        Vector<String>
    >;

    /// 配置格式枚举
    enum class ConfigFormat_HHBUI : uint32_t {
        JSON = 0,
        XML = 1,
        INI = 2,
        YAML = 3,
        Binary = 4,
        Registry = 5  // Windows注册表
    };

    /// 配置访问权限
    enum class ConfigAccess_HHBUI : uint32_t {
        ReadOnly = 0,
        WriteOnly = 1,
        ReadWrite = 2
    };

    /// 配置变更事件类型
    enum class ConfigChangeEvent_HHBUI : uint32_t {
        ValueChanged = 0,
        SectionAdded = 1,
        SectionRemoved = 2,
        FileReloaded = 3,
        ValidationFailed = 4
    };

    /// 配置变更监听器
    using ConfigChangeListener_HHBUI = Function<void(
        StringView sectionName,
        StringView keyName,
        const ConfigValue_HHBUI& oldValue,
        const ConfigValue_HHBUI& newValue,
        ConfigChangeEvent_HHBUI eventType
    )>;

    /// 配置节信息
    struct ConfigSection_HHBUI {
        String name;
        String description;
        HashMap<String, ConfigValue_HHBUI> values;
        HashMap<String, String> comments;
        ConfigAccess_HHBUI access = ConfigAccess_HHBUI::ReadWrite;
        bool encrypted = false;
        std::chrono::system_clock::time_point lastModified;
    };

    /// 现代化配置管理器类
    class ApplicationConfig_HHBUI {
    public:
        /// 构造函数
        explicit ApplicationConfig_HHBUI(ConfigFormat_HHBUI format = ConfigFormat_HHBUI::JSON) noexcept;
        
        /// 析构函数
        ~ApplicationConfig_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        ApplicationConfig_HHBUI(const ApplicationConfig_HHBUI&) = delete;
        ApplicationConfig_HHBUI& operator=(const ApplicationConfig_HHBUI&) = delete;

        /// 启用移动构造和赋值
        ApplicationConfig_HHBUI(ApplicationConfig_HHBUI&&) noexcept = default;
        ApplicationConfig_HHBUI& operator=(ApplicationConfig_HHBUI&&) noexcept = default;

        /// 从文件加载配置
        [[nodiscard]] bool loadFromFile_HHBUI(const std::filesystem::path& filePath) noexcept;

        /// 保存配置到文件
        [[nodiscard]] bool saveToFile_HHBUI(const std::filesystem::path& filePath) const noexcept;

        /// 从字符串加载配置
        [[nodiscard]] bool loadFromString_HHBUI(StringView configData) noexcept;

        /// 导出配置为字符串
        [[nodiscard]] Optional<String> saveToString_HHBUI() const noexcept;

        /// 重新加载配置文件
        [[nodiscard]] bool reload_HHBUI() noexcept;

        /// 检查配置是否已修改
        [[nodiscard]] bool isModified_HHBUI() const noexcept;

        /// 标记配置为已修改
        void markModified_HHBUI() noexcept;

        /// 清除修改标记
        void clearModified_HHBUI() noexcept;

        /// 获取配置值（类型安全）
        template<typename T>
        [[nodiscard]] Optional<T> getValue_HHBUI(StringView sectionName, StringView keyName) const noexcept;

        /// 设置配置值（类型安全）
        template<typename T>
        [[nodiscard]] bool setValue_HHBUI(StringView sectionName, StringView keyName, const T& value) noexcept;

        /// 获取配置值（带默认值）
        template<typename T>
        [[nodiscard]] T getValueOrDefault_HHBUI(StringView sectionName, StringView keyName, const T& defaultValue) const noexcept;

        /// 检查配置键是否存在
        [[nodiscard]] bool hasKey_HHBUI(StringView sectionName, StringView keyName) const noexcept;

        /// 检查配置节是否存在
        [[nodiscard]] bool hasSection_HHBUI(StringView sectionName) const noexcept;

        /// 创建配置节
        [[nodiscard]] bool createSection_HHBUI(StringView sectionName, StringView description = {}) noexcept;

        /// 删除配置节
        [[nodiscard]] bool removeSection_HHBUI(StringView sectionName) noexcept;

        /// 删除配置键
        [[nodiscard]] bool removeKey_HHBUI(StringView sectionName, StringView keyName) noexcept;

        /// 获取所有节名称
        [[nodiscard]] Vector<String> getSectionNames_HHBUI() const noexcept;

        /// 获取节中所有键名称
        [[nodiscard]] Vector<String> getKeyNames_HHBUI(StringView sectionName) const noexcept;

        /// 获取节信息
        [[nodiscard]] Optional<ConfigSection_HHBUI> getSection_HHBUI(StringView sectionName) const noexcept;

        /// 设置配置格式
        void setFormat_HHBUI(ConfigFormat_HHBUI format) noexcept;

        /// 获取配置格式
        [[nodiscard]] ConfigFormat_HHBUI getFormat_HHBUI() const noexcept;

        /// 启用/禁用加密
        void setEncryption_HHBUI(bool enabled, StringView password = {}) noexcept;

        /// 检查是否启用加密
        [[nodiscard]] bool isEncryptionEnabled_HHBUI() const noexcept;

        /// 注册变更监听器
        void registerChangeListener_HHBUI(StringView sectionName, ConfigChangeListener_HHBUI listener) noexcept;

        /// 注销变更监听器
        void unregisterChangeListener_HHBUI(StringView sectionName) noexcept;

        /// 验证配置
        [[nodiscard]] bool validate_HHBUI() const noexcept;

        /// 设置验证器
        void setValidator_HHBUI(UniquePtr<ConfigValidator_HHBUI> validator) noexcept;

        /// 获取最后错误信息
        [[nodiscard]] const String& getLastError_HHBUI() const noexcept;

        /// 清除所有配置
        void clear_HHBUI() noexcept;

        /// 获取配置统计信息
        struct ConfigStats_HHBUI {
            size_t sectionCount = 0;
            size_t keyCount = 0;
            size_t memoryUsage = 0;
            std::chrono::system_clock::time_point lastAccess;
            std::chrono::system_clock::time_point lastModified;
        };
        [[nodiscard]] ConfigStats_HHBUI getStats_HHBUI() const noexcept;

    private:
        /// 内部加载实现
        [[nodiscard]] bool internalLoad_HHBUI(StringView data) noexcept;

        /// 内部保存实现
        [[nodiscard]] Optional<String> internalSave_HHBUI() const noexcept;

        /// 触发变更事件
        void triggerChangeEvent_HHBUI(StringView sectionName, StringView keyName,
                                     const ConfigValue_HHBUI& oldValue,
                                     const ConfigValue_HHBUI& newValue,
                                     ConfigChangeEvent_HHBUI eventType) noexcept;

        /// 更新访问时间
        void updateAccessTime_HHBUI() const noexcept;

    private:
        mutable std::shared_mutex configMutex_{};
        HashMap<String, ConfigSection_HHBUI> sections_{};
        HashMap<String, Vector<ConfigChangeListener_HHBUI>> changeListeners_{};
        
        ConfigFormat_HHBUI format_{ConfigFormat_HHBUI::JSON};
        std::filesystem::path filePath_{};
        bool modified_{false};
        bool encryptionEnabled_{false};
        String encryptionPassword_{};
        
        UniquePtr<ConfigValidator_HHBUI> validator_{};
        UniquePtr<ConfigEncryption_HHBUI> encryption_{};
        
        mutable String lastError_{};
        mutable std::chrono::system_clock::time_point lastAccessTime_{};
        std::chrono::system_clock::time_point lastModifiedTime_{};
    };

    /// 模板实现
    template<typename T>
    Optional<T> ApplicationConfig_HHBUI::getValue_HHBUI(StringView sectionName, StringView keyName) const noexcept {
        std::shared_lock lock(configMutex_);
        updateAccessTime_HHBUI();
        
        auto sectionIt = sections_.find(String(sectionName));
        if (sectionIt == sections_.end()) {
            return std::nullopt;
        }
        
        auto keyIt = sectionIt->second.values.find(String(keyName));
        if (keyIt == sectionIt->second.values.end()) {
            return std::nullopt;
        }
        
        try {
            return std::get<T>(keyIt->second);
        } catch (...) {
            return std::nullopt;
        }
    }

    template<typename T>
    bool ApplicationConfig_HHBUI::setValue_HHBUI(StringView sectionName, StringView keyName, const T& value) noexcept {
        std::unique_lock lock(configMutex_);
        
        try {
            String sectionStr(sectionName);
            String keyStr(keyName);
            
            auto& section = sections_[sectionStr];
            if (section.name.empty()) {
                section.name = sectionStr;
                section.lastModified = std::chrono::system_clock::now();
            }
            
            ConfigValue_HHBUI oldValue;
            auto keyIt = section.values.find(keyStr);
            if (keyIt != section.values.end()) {
                oldValue = keyIt->second;
            }
            
            ConfigValue_HHBUI newValue = value;
            section.values[keyStr] = newValue;
            section.lastModified = std::chrono::system_clock::now();
            
            modified_ = true;
            lastModifiedTime_ = std::chrono::system_clock::now();
            
            // 触发变更事件
            triggerChangeEvent_HHBUI(sectionName, keyName, oldValue, newValue, ConfigChangeEvent_HHBUI::ValueChanged);
            
            return true;
        } catch (...) {
            return false;
        }
    }

    template<typename T>
    T ApplicationConfig_HHBUI::getValueOrDefault_HHBUI(StringView sectionName, StringView keyName, const T& defaultValue) const noexcept {
        auto value = getValue_HHBUI<T>(sectionName, keyName);
        return value.value_or(defaultValue);
    }

} // namespace hhbui::modern
