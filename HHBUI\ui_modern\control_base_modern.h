/**
** =====================================================================================
**
**       文件名称: control_base_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化UI控件基类 - UI控件基类 （声明文件）
**
**       主要功能:
**       - 现代化UI控件基础架构与继承体系
**       - 高性能事件处理与消息分发系统
**       - 智能布局管理与自适应调整
**       - 异常安全的UI操作接口
**       - 跨平台UI控件抽象与兼容
**       - UI性能监控与调试支持
**       - 现代化样式与主题系统
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与异常安全保证
**       - 高性能事件处理与消息队列
**       - 模板元编程与编译时优化
**       - 多线程安全的UI操作
**       - 标准库容器与算法集成
**       - 跨平台UI抽象层设计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化UI控件基类
**                             2. 实现智能事件处理机制
**                             3. 添加高性能布局系统
**                             4. 集成异常安全保证机制
**                             5. 优化UI渲染性能
**                             6. 确保跨平台兼容性
**                             7. 添加UI调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_map>
#include <optional>
#include <functional>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <Windows.h>

namespace hhbui::modern {

    // 前向声明
    class Window_HHBUI;
    class Canvas_HHBUI;
    class Layout_HHBUI;
    class Animation_HHBUI;

    /// UI控件状态枚举
    enum class ControlState_HHBUI : uint32_t {
        Normal = 0,
        Hover = 1,
        Pressed = 2,
        Focused = 3,
        Disabled = 4,
        Selected = 5,
        Checked = 6
    };

    /// UI控件样式枚举
    enum class ControlStyle_HHBUI : uint32_t {
        None = 0,
        Border = 1,
        Shadow = 2,
        Rounded = 4,
        Transparent = 8,
        Gradient = 16,
        Animation = 32,
        ThreeD = 64
    };

    /// UI控件扩展样式枚举
    enum class ControlStyleEx_HHBUI : uint32_t {
        None = 0,
        DoubleBuffer = 1,
        Layered = 2,
        Composited = 4,
        NoActivate = 8,
        TopMost = 16,
        ToolWindow = 32,
        AcceptFiles = 64
    };

    /// 文本格式枚举
    enum class TextFormat_HHBUI : uint32_t {
        Left = 0,
        Center = 1,
        Right = 2,
        Top = 0,
        VCenter = 4,
        Bottom = 8,
        WordWrap = 16,
        SingleLine = 32,
        NoClip = 64,
        ExpandTabs = 128
    };

    /// UI颜色结构
    struct UIColor_HHBUI {
        float r = 0.0f;
        float g = 0.0f;
        float b = 0.0f;
        float a = 1.0f;

        UIColor_HHBUI() = default;
        UIColor_HHBUI(float red, float green, float blue, float alpha = 1.0f) noexcept
            : r(red), g(green), b(blue), a(alpha) {}
        
        explicit UIColor_HHBUI(uint32_t argb) noexcept {
            a = ((argb >> 24) & 0xFF) / 255.0f;
            r = ((argb >> 16) & 0xFF) / 255.0f;
            g = ((argb >> 8) & 0xFF) / 255.0f;
            b = (argb & 0xFF) / 255.0f;
        }

        [[nodiscard]] uint32_t toARGB_HHBUI() const noexcept {
            return (static_cast<uint32_t>(a * 255) << 24) |
                   (static_cast<uint32_t>(r * 255) << 16) |
                   (static_cast<uint32_t>(g * 255) << 8) |
                   static_cast<uint32_t>(b * 255);
        }
    };

    /// UI矩形结构
    struct UIRect_HHBUI {
        float left = 0.0f;
        float top = 0.0f;
        float right = 0.0f;
        float bottom = 0.0f;

        UIRect_HHBUI() = default;
        UIRect_HHBUI(float l, float t, float r, float b) noexcept
            : left(l), top(t), right(r), bottom(b) {}

        [[nodiscard]] float width_HHBUI() const noexcept { return right - left; }
        [[nodiscard]] float height_HHBUI() const noexcept { return bottom - top; }
        [[nodiscard]] bool isEmpty_HHBUI() const noexcept { return width_HHBUI() <= 0 || height_HHBUI() <= 0; }
        
        void offset_HHBUI(float dx, float dy) noexcept {
            left += dx; top += dy; right += dx; bottom += dy;
        }
        
        void inflate_HHBUI(float dx, float dy) noexcept {
            left -= dx; top -= dy; right += dx; bottom += dy;
        }
    };

    /// UI点结构
    struct UIPoint_HHBUI {
        float x = 0.0f;
        float y = 0.0f;

        UIPoint_HHBUI() = default;
        UIPoint_HHBUI(float px, float py) noexcept : x(px), y(py) {}
    };

    /// UI尺寸结构
    struct UISize_HHBUI {
        float width = 0.0f;
        float height = 0.0f;

        UISize_HHBUI() = default;
        UISize_HHBUI(float w, float h) noexcept : width(w), height(h) {}
    };

    /// 事件处理器类型
    using EventHandler_HHBUI = Function<LRESULT(HWND, UINT, WPARAM, LPARAM)>;
    using PaintHandler_HHBUI = Function<void(Canvas_HHBUI*)>;
    using LayoutHandler_HHBUI = Function<void(const UIRect_HHBUI&)>;

    /// 现代化UI控件基类
    class ControlBase_HHBUI {
    public:
        /// 构造函数
        explicit ControlBase_HHBUI(StringView name = L"HHBUI_Control") noexcept;

        /// 虚析构函数
        virtual ~ControlBase_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        ControlBase_HHBUI(const ControlBase_HHBUI&) = delete;
        ControlBase_HHBUI& operator=(const ControlBase_HHBUI&) = delete;

        /// 启用移动构造和赋值
        ControlBase_HHBUI(ControlBase_HHBUI&&) noexcept = default;
        ControlBase_HHBUI& operator=(ControlBase_HHBUI&&) noexcept = default;

        /// 初始化控件
        [[nodiscard]] virtual bool initialize_HHBUI(
            ControlBase_HHBUI* parent,
            const UIRect_HHBUI& bounds,
            ControlStyle_HHBUI style = ControlStyle_HHBUI::None,
            ControlStyleEx_HHBUI styleEx = ControlStyleEx_HHBUI::None,
            int32_t id = 0
        ) noexcept;

        /// 销毁控件
        virtual void destroy_HHBUI() noexcept;

        /// 显示控件
        virtual void show_HHBUI(bool visible = true) noexcept;

        /// 隐藏控件
        virtual void hide_HHBUI() noexcept;

        /// 启用控件
        virtual void enable_HHBUI(bool enabled = true) noexcept;

        /// 禁用控件
        virtual void disable_HHBUI() noexcept;

        /// 设置焦点
        virtual void setFocus_HHBUI() noexcept;

        /// 移除焦点
        virtual void killFocus_HHBUI() noexcept;

        /// 刷新控件
        virtual void refresh_HHBUI() noexcept;

        /// 使控件无效（触发重绘）
        virtual void invalidate_HHBUI(const Optional<UIRect_HHBUI>& rect = std::nullopt) noexcept;

        /// 更新控件
        virtual void update_HHBUI() noexcept;

        /// 移动控件
        virtual void move_HHBUI(const UIPoint_HHBUI& position) noexcept;

        /// 调整控件大小
        virtual void resize_HHBUI(const UISize_HHBUI& size) noexcept;

        /// 设置控件边界
        virtual void setBounds_HHBUI(const UIRect_HHBUI& bounds) noexcept;

        /// 获取控件边界
        [[nodiscard]] virtual UIRect_HHBUI getBounds_HHBUI() const noexcept;

        /// 获取客户区边界
        [[nodiscard]] virtual UIRect_HHBUI getClientBounds_HHBUI() const noexcept;

        /// 设置控件名称
        virtual void setName_HHBUI(StringView name) noexcept;

        /// 获取控件名称
        [[nodiscard]] virtual const String& getName_HHBUI() const noexcept;

        /// 设置控件文本
        virtual void setText_HHBUI(StringView text) noexcept;

        /// 获取控件文本
        [[nodiscard]] virtual const String& getText_HHBUI() const noexcept;

        /// 设置控件ID
        virtual void setId_HHBUI(int32_t id) noexcept;

        /// 获取控件ID
        [[nodiscard]] virtual int32_t getId_HHBUI() const noexcept;

        /// 设置控件状态
        virtual void setState_HHBUI(ControlState_HHBUI state) noexcept;

        /// 获取控件状态
        [[nodiscard]] virtual ControlState_HHBUI getState_HHBUI() const noexcept;

        /// 设置控件样式
        virtual void setStyle_HHBUI(ControlStyle_HHBUI style) noexcept;

        /// 获取控件样式
        [[nodiscard]] virtual ControlStyle_HHBUI getStyle_HHBUI() const noexcept;

        /// 设置控件扩展样式
        virtual void setStyleEx_HHBUI(ControlStyleEx_HHBUI styleEx) noexcept;

        /// 获取控件扩展样式
        [[nodiscard]] virtual ControlStyleEx_HHBUI getStyleEx_HHBUI() const noexcept;

        /// 设置背景颜色
        virtual void setBackgroundColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取背景颜色
        [[nodiscard]] virtual UIColor_HHBUI getBackgroundColor_HHBUI() const noexcept;

        /// 设置前景颜色
        virtual void setForegroundColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取前景颜色
        [[nodiscard]] virtual UIColor_HHBUI getForegroundColor_HHBUI() const noexcept;

        /// 设置边框颜色
        virtual void setBorderColor_HHBUI(const UIColor_HHBUI& color) noexcept;

        /// 获取边框颜色
        [[nodiscard]] virtual UIColor_HHBUI getBorderColor_HHBUI() const noexcept;

        /// 检查控件是否可见
        [[nodiscard]] virtual bool isVisible_HHBUI() const noexcept;

        /// 检查控件是否启用
        [[nodiscard]] virtual bool isEnabled_HHBUI() const noexcept;

        /// 检查控件是否有焦点
        [[nodiscard]] virtual bool hasFocus_HHBUI() const noexcept;

        /// 获取父控件
        [[nodiscard]] virtual ControlBase_HHBUI* getParent_HHBUI() const noexcept;

        /// 设置父控件
        virtual void setParent_HHBUI(ControlBase_HHBUI* parent) noexcept;

        /// 添加子控件
        virtual void addChild_HHBUI(UniquePtr<ControlBase_HHBUI> child) noexcept;

        /// 移除子控件
        virtual void removeChild_HHBUI(ControlBase_HHBUI* child) noexcept;

        /// 获取子控件数量
        [[nodiscard]] virtual size_t getChildCount_HHBUI() const noexcept;

        /// 获取子控件
        [[nodiscard]] virtual ControlBase_HHBUI* getChild_HHBUI(size_t index) const noexcept;

        /// 查找子控件
        [[nodiscard]] virtual ControlBase_HHBUI* findChild_HHBUI(StringView name) const noexcept;
        [[nodiscard]] virtual ControlBase_HHBUI* findChild_HHBUI(int32_t id) const noexcept;

        /// 注册事件处理器
        virtual void registerEventHandler_HHBUI(UINT message, EventHandler_HHBUI handler) noexcept;

        /// 注销事件处理器
        virtual void unregisterEventHandler_HHBUI(UINT message) noexcept;

        /// 设置绘制处理器
        virtual void setPaintHandler_HHBUI(PaintHandler_HHBUI handler) noexcept;

        /// 设置布局处理器
        virtual void setLayoutHandler_HHBUI(LayoutHandler_HHBUI handler) noexcept;

    protected:
        /// 消息处理函数（子类重写）
        virtual LRESULT onMessage_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept;

        /// 绘制函数（子类重写）
        virtual void onPaint_HHBUI(Canvas_HHBUI* canvas) noexcept;

        /// 布局函数（子类重写）
        virtual void onLayout_HHBUI(const UIRect_HHBUI& clientBounds) noexcept;

        /// 状态变更通知
        virtual void onStateChanged_HHBUI(ControlState_HHBUI oldState, ControlState_HHBUI newState) noexcept;

        /// 样式变更通知
        virtual void onStyleChanged_HHBUI(ControlStyle_HHBUI oldStyle, ControlStyle_HHBUI newStyle) noexcept;

        /// 大小变更通知
        virtual void onSizeChanged_HHBUI(const UISize_HHBUI& oldSize, const UISize_HHBUI& newSize) noexcept;

        /// 位置变更通知
        virtual void onPositionChanged_HHBUI(const UIPoint_HHBUI& oldPosition, const UIPoint_HHBUI& newPosition) noexcept;

    private:
        /// 内部消息分发
        LRESULT internalMessageDispatch_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept;

        /// 更新控件状态
        void updateControlState_HHBUI() noexcept;

    protected:
        // 基本属性
        String name_{};
        String text_{};
        int32_t id_ = 0;
        std::atomic<ControlState_HHBUI> state_{ControlState_HHBUI::Normal};
        ControlStyle_HHBUI style_{ControlStyle_HHBUI::None};
        ControlStyleEx_HHBUI styleEx_{ControlStyleEx_HHBUI::None};

        // 几何属性
        UIRect_HHBUI bounds_{};
        UIRect_HHBUI clientBounds_{};

        // 颜色属性
        UIColor_HHBUI backgroundColor_{1.0f, 1.0f, 1.0f, 1.0f};
        UIColor_HHBUI foregroundColor_{0.0f, 0.0f, 0.0f, 1.0f};
        UIColor_HHBUI borderColor_{0.5f, 0.5f, 0.5f, 1.0f};

        // 状态标志
        std::atomic<bool> visible_{true};
        std::atomic<bool> enabled_{true};
        std::atomic<bool> focused_{false};
        std::atomic<bool> destroyed_{false};

        // 层次关系
        ControlBase_HHBUI* parent_ = nullptr;
        Vector<UniquePtr<ControlBase_HHBUI>> children_{};
        mutable std::shared_mutex childrenMutex_{};

        // 事件处理
        HashMap<UINT, EventHandler_HHBUI> eventHandlers_{};
        Optional<PaintHandler_HHBUI> paintHandler_{};
        Optional<LayoutHandler_HHBUI> layoutHandler_{};
        mutable std::shared_mutex handlersMutex_{};

        // 子系统
        UniquePtr<Layout_HHBUI> layout_{};
        UniquePtr<Animation_HHBUI> animation_{};
    };

    /// 全局UI控件函数

    /// 创建控件
    template<typename ControlType, typename... Args>
    [[nodiscard]] UniquePtr<ControlType> createControl_HHBUI(Args&&... args) noexcept;

    /// 查找控件
    [[nodiscard]] ControlBase_HHBUI* findControlByName_HHBUI(ControlBase_HHBUI* root, StringView name) noexcept;
    [[nodiscard]] ControlBase_HHBUI* findControlById_HHBUI(ControlBase_HHBUI* root, int32_t id) noexcept;

    /// 遍历控件树
    void traverseControls_HHBUI(ControlBase_HHBUI* root, Function<void(ControlBase_HHBUI*)> visitor) noexcept;

    // ========== 模板实现 ==========

    template<typename ControlType, typename... Args>
    UniquePtr<ControlType> createControl_HHBUI(Args&&... args) noexcept {
        static_assert(std::is_base_of_v<ControlBase_HHBUI, ControlType>, "ControlType must derive from ControlBase_HHBUI");
        
        try {
            return std::make_unique<ControlType>(std::forward<Args>(args)...);
        } catch (...) {
            return nullptr;
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIControl = hhbui::modern::ControlBase_HHBUI;
    using UIColor = hhbui::modern::UIColor_HHBUI;
    using ExRectF = hhbui::modern::UIRect_HHBUI;
    using UIBase = hhbui::modern::ControlBase_HHBUI;
}
