/**
** =====================================================================================
**
**       文件名称: threading_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化线程管理系统 - 线程管理 （声明文件）
**
**       主要功能:
**       - 现代化多线程编程与并发控制
**       - 高性能线程池与任务调度系统
**       - 智能线程同步与锁管理
**       - 异常安全的并发操作接口
**       - 跨平台线程抽象与管理
**       - 线程性能监控与调试支持
**       - 现代化异步编程模式
**
**       技术特性:
**       - 采用现代C++17标准并发库
**       - 智能锁与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能无锁数据结构优化
**       - 模板元编程与编译时优化
**       - 标准库并发算法集成
**       - 跨平台线程管理抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化线程管理系统
**                             2. 实现智能锁与RAII管理
**                             3. 添加高性能线程池系统
**                             4. 集成异常安全保证机制
**                             5. 优化并发操作性能
**                             6. 确保跨平台兼容性
**                             7. 添加线程调试支持
**
** =====================================================================================
**/

#pragma once

#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <atomic>
#include <future>
#include <functional>
#include <queue>
#include <vector>
#include <memory>
#include <chrono>
#include <optional>
#include <string>
#include <string_view>
#include <type_traits>
#include <execution>

namespace hhbui::modern {

    // 前向声明
    class ThreadPool_HHBUI;
    class TaskScheduler_HHBUI;
    class ThreadMonitor_HHBUI;

    /// 线程优先级枚举
    enum class ThreadPriority_HHBUI : int32_t {
        Idle = -2,
        BelowNormal = -1,
        Normal = 0,
        AboveNormal = 1,
        High = 2,
        Critical = 3
    };

    /// 线程状态枚举
    enum class ThreadState_HHBUI : uint32_t {
        Created = 0,
        Running = 1,
        Paused = 2,
        Stopped = 3,
        Error = 4
    };

    /// 任务优先级枚举
    enum class TaskPriority_HHBUI : uint32_t {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    };

    /// 线程统计信息
    struct ThreadStats_HHBUI {
        std::thread::id threadId;
        String threadName;
        ThreadState_HHBUI state = ThreadState_HHBUI::Created;
        ThreadPriority_HHBUI priority = ThreadPriority_HHBUI::Normal;
        std::chrono::steady_clock::time_point startTime;
        std::chrono::steady_clock::duration runningTime{};
        uint64_t tasksExecuted = 0;
        uint64_t exceptionsThrown = 0;
        double cpuUsage = 0.0;
        size_t memoryUsage = 0;
    };

    /// 现代化线程安全队列模板类
    template<typename T>
    class ThreadSafeQueue_HHBUI {
    public:
        /// 构造函数
        ThreadSafeQueue_HHBUI() = default;

        /// 析构函数
        ~ThreadSafeQueue_HHBUI() = default;

        /// 禁用拷贝构造和赋值
        ThreadSafeQueue_HHBUI(const ThreadSafeQueue_HHBUI&) = delete;
        ThreadSafeQueue_HHBUI& operator=(const ThreadSafeQueue_HHBUI&) = delete;

        /// 启用移动构造和赋值
        ThreadSafeQueue_HHBUI(ThreadSafeQueue_HHBUI&&) noexcept = default;
        ThreadSafeQueue_HHBUI& operator=(ThreadSafeQueue_HHBUI&&) noexcept = default;

        /// 入队（拷贝）
        void push_HHBUI(const T& item) noexcept;

        /// 入队（移动）
        void push_HHBUI(T&& item) noexcept;

        /// 优先入队（插入到队首）
        void pushFront_HHBUI(const T& item) noexcept;
        void pushFront_HHBUI(T&& item) noexcept;

        /// 出队（阻塞）
        [[nodiscard]] T pop_HHBUI() noexcept;

        /// 尝试出队（非阻塞）
        [[nodiscard]] Optional<T> tryPop_HHBUI() noexcept;

        /// 带超时的出队
        template<typename Rep, typename Period>
        [[nodiscard]] Optional<T> tryPopFor_HHBUI(const std::chrono::duration<Rep, Period>& timeout) noexcept;

        /// 检查是否为空
        [[nodiscard]] bool empty_HHBUI() const noexcept;

        /// 获取队列大小
        [[nodiscard]] size_t size_HHBUI() const noexcept;

        /// 清空队列
        void clear_HHBUI() noexcept;

        /// 设置最大容量
        void setMaxCapacity_HHBUI(size_t maxCapacity) noexcept;

        /// 获取最大容量
        [[nodiscard]] size_t getMaxCapacity_HHBUI() const noexcept;

    private:
        mutable std::mutex mutex_{};
        std::condition_variable condition_{};
        std::queue<T> queue_{};
        size_t maxCapacity_{SIZE_MAX};
    };

    /// 现代化读写锁包装器
    class ReadWriteLock_HHBUI {
    public:
        /// 构造函数
        ReadWriteLock_HHBUI() = default;

        /// 析构函数
        ~ReadWriteLock_HHBUI() = default;

        /// 禁用拷贝构造和赋值
        ReadWriteLock_HHBUI(const ReadWriteLock_HHBUI&) = delete;
        ReadWriteLock_HHBUI& operator=(const ReadWriteLock_HHBUI&) = delete;

        /// 读锁RAII包装器
        class ReadLock_HHBUI {
        public:
            explicit ReadLock_HHBUI(ReadWriteLock_HHBUI& rwLock) noexcept;
            ~ReadLock_HHBUI() noexcept;
            ReadLock_HHBUI(const ReadLock_HHBUI&) = delete;
            ReadLock_HHBUI& operator=(const ReadLock_HHBUI&) = delete;
            ReadLock_HHBUI(ReadLock_HHBUI&&) noexcept = default;
            ReadLock_HHBUI& operator=(ReadLock_HHBUI&&) noexcept = default;

        private:
            std::shared_lock<std::shared_mutex> lock_;
        };

        /// 写锁RAII包装器
        class WriteLock_HHBUI {
        public:
            explicit WriteLock_HHBUI(ReadWriteLock_HHBUI& rwLock) noexcept;
            ~WriteLock_HHBUI() noexcept;
            WriteLock_HHBUI(const WriteLock_HHBUI&) = delete;
            WriteLock_HHBUI& operator=(const WriteLock_HHBUI&) = delete;
            WriteLock_HHBUI(WriteLock_HHBUI&&) noexcept = default;
            WriteLock_HHBUI& operator=(WriteLock_HHBUI&&) noexcept = default;

        private:
            std::unique_lock<std::shared_mutex> lock_;
        };

        /// 获取读锁
        [[nodiscard]] ReadLock_HHBUI readLock_HHBUI() noexcept;

        /// 获取写锁
        [[nodiscard]] WriteLock_HHBUI writeLock_HHBUI() noexcept;

    private:
        std::shared_mutex mutex_{};
    };

    /// 现代化线程类
    class Thread_HHBUI {
    public:
        /// 构造函数
        explicit Thread_HHBUI(StringView name = L"HHBUI_Thread") noexcept;

        /// 析构函数
        virtual ~Thread_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        Thread_HHBUI(const Thread_HHBUI&) = delete;
        Thread_HHBUI& operator=(const Thread_HHBUI&) = delete;

        /// 启用移动构造和赋值
        Thread_HHBUI(Thread_HHBUI&&) noexcept = default;
        Thread_HHBUI& operator=(Thread_HHBUI&&) noexcept = default;

        /// 启动线程
        [[nodiscard]] bool start_HHBUI(bool paused = false) noexcept;

        /// 停止线程
        void stop_HHBUI() noexcept;

        /// 暂停线程
        void pause_HHBUI() noexcept;

        /// 恢复线程
        void resume_HHBUI() noexcept;

        /// 等待线程结束
        void join_HHBUI() noexcept;

        /// 分离线程
        void detach_HHBUI() noexcept;

        /// 检查线程是否可连接
        [[nodiscard]] bool joinable_HHBUI() const noexcept;

        /// 获取线程状态
        [[nodiscard]] ThreadState_HHBUI getState_HHBUI() const noexcept;

        /// 设置线程优先级
        void setPriority_HHBUI(ThreadPriority_HHBUI priority) noexcept;

        /// 获取线程优先级
        [[nodiscard]] ThreadPriority_HHBUI getPriority_HHBUI() const noexcept;

        /// 设置线程名称
        void setName_HHBUI(StringView name) noexcept;

        /// 获取线程名称
        [[nodiscard]] const String& getName_HHBUI() const noexcept;

        /// 获取线程ID
        [[nodiscard]] std::thread::id getId_HHBUI() const noexcept;

        /// 获取线程统计信息
        [[nodiscard]] ThreadStats_HHBUI getStats_HHBUI() const noexcept;

        /// 线程主函数（需要子类重写）
        virtual void run_HHBUI() = 0;

        /// 获取当前线程ID
        [[nodiscard]] static std::thread::id getCurrentThreadId_HHBUI() noexcept;

        /// 让出CPU时间片
        static void yield_HHBUI() noexcept;

        /// 线程休眠
        template<typename Rep, typename Period>
        static void sleep_HHBUI(const std::chrono::duration<Rep, Period>& duration) noexcept;

    protected:
        /// 线程执行前回调
        virtual void onBeforeRun_HHBUI() noexcept {}

        /// 线程执行后回调
        virtual void onAfterRun_HHBUI() noexcept {}

        /// 线程异常处理回调
        virtual void onException_HHBUI(const std::exception& e) noexcept {}

    private:
        /// 内部线程函数
        void internalRun_HHBUI() noexcept;

        /// 更新统计信息
        void updateStats_HHBUI() noexcept;

    private:
        UniquePtr<std::thread> thread_{};
        String name_;
        std::atomic<ThreadState_HHBUI> state_{ThreadState_HHBUI::Created};
        std::atomic<ThreadPriority_HHBUI> priority_{ThreadPriority_HHBUI::Normal};
        
        std::mutex controlMutex_{};
        std::condition_variable pauseCondition_{};
        std::atomic<bool> shouldStop_{false};
        std::atomic<bool> shouldPause_{false};
        
        ThreadStats_HHBUI stats_{};
        mutable std::mutex statsMutex_{};
    };

    /// 现代化任务类
    template<typename ResultType = void>
    class Task_HHBUI {
    public:
        /// 任务函数类型
        using TaskFunction_HHBUI = std::function<ResultType()>;

        /// 构造函数
        explicit Task_HHBUI(
            TaskFunction_HHBUI function,
            TaskPriority_HHBUI priority = TaskPriority_HHBUI::Normal,
            StringView name = L"HHBUI_Task"
        ) noexcept;

        /// 析构函数
        ~Task_HHBUI() = default;

        /// 禁用拷贝构造和赋值
        Task_HHBUI(const Task_HHBUI&) = delete;
        Task_HHBUI& operator=(const Task_HHBUI&) = delete;

        /// 启用移动构造和赋值
        Task_HHBUI(Task_HHBUI&&) noexcept = default;
        Task_HHBUI& operator=(Task_HHBUI&&) noexcept = default;

        /// 执行任务
        void execute_HHBUI() noexcept;

        /// 获取任务结果
        [[nodiscard]] std::future<ResultType> getFuture_HHBUI() noexcept;

        /// 获取任务优先级
        [[nodiscard]] TaskPriority_HHBUI getPriority_HHBUI() const noexcept;

        /// 设置任务优先级
        void setPriority_HHBUI(TaskPriority_HHBUI priority) noexcept;

        /// 获取任务名称
        [[nodiscard]] const String& getName_HHBUI() const noexcept;

        /// 设置任务名称
        void setName_HHBUI(StringView name) noexcept;

        /// 检查任务是否已完成
        [[nodiscard]] bool isCompleted_HHBUI() const noexcept;

        /// 取消任务
        void cancel_HHBUI() noexcept;

        /// 检查任务是否已取消
        [[nodiscard]] bool isCancelled_HHBUI() const noexcept;

    private:
        TaskFunction_HHBUI function_;
        std::promise<ResultType> promise_;
        TaskPriority_HHBUI priority_;
        String name_;
        std::atomic<bool> completed_{false};
        std::atomic<bool> cancelled_{false};
    };

    /// 全局线程管理函数

    /// 创建并启动线程
    template<typename Func, typename... Args>
    [[nodiscard]] UniquePtr<std::thread> createThread_HHBUI(Func&& func, Args&&... args) noexcept;

    /// 异步执行任务
    template<typename Func, typename... Args>
    [[nodiscard]] auto async_HHBUI(Func&& func, Args&&... args) noexcept;

    /// 并行执行算法
    template<typename ExecutionPolicy, typename ForwardIt, typename UnaryFunction>
    void parallelForEach_HHBUI(ExecutionPolicy&& policy, ForwardIt first, ForwardIt last, UnaryFunction func) noexcept;

    /// 获取硬件并发数
    [[nodiscard]] uint32_t getHardwareConcurrency_HHBUI() noexcept;

    /// 设置线程亲和性
    bool setThreadAffinity_HHBUI(std::thread::id threadId, uint64_t affinityMask) noexcept;

    // ========== 模板实现 ==========

    template<typename T>
    void ThreadSafeQueue_HHBUI<T>::push_HHBUI(const T& item) noexcept {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() < maxCapacity_) {
            queue_.push(item);
            condition_.notify_one();
        }
    }

    template<typename T>
    void ThreadSafeQueue_HHBUI<T>::push_HHBUI(T&& item) noexcept {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() < maxCapacity_) {
            queue_.push(std::move(item));
            condition_.notify_one();
        }
    }

    template<typename T>
    T ThreadSafeQueue_HHBUI<T>::pop_HHBUI() noexcept {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return !queue_.empty(); });
        
        T result = std::move(queue_.front());
        queue_.pop();
        return result;
    }

    template<typename T>
    Optional<T> ThreadSafeQueue_HHBUI<T>::tryPop_HHBUI() noexcept {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return std::nullopt;
        }
        
        T result = std::move(queue_.front());
        queue_.pop();
        return result;
    }

    template<typename T>
    template<typename Rep, typename Period>
    Optional<T> ThreadSafeQueue_HHBUI<T>::tryPopFor_HHBUI(const std::chrono::duration<Rep, Period>& timeout) noexcept {
        std::unique_lock<std::mutex> lock(mutex_);
        if (condition_.wait_for(lock, timeout, [this] { return !queue_.empty(); })) {
            T result = std::move(queue_.front());
            queue_.pop();
            return result;
        }
        return std::nullopt;
    }

    template<typename Rep, typename Period>
    void Thread_HHBUI::sleep_HHBUI(const std::chrono::duration<Rep, Period>& duration) noexcept {
        std::this_thread::sleep_for(duration);
    }

    template<typename Func, typename... Args>
    UniquePtr<std::thread> createThread_HHBUI(Func&& func, Args&&... args) noexcept {
        try {
            return std::make_unique<std::thread>(std::forward<Func>(func), std::forward<Args>(args)...);
        } catch (...) {
            return nullptr;
        }
    }

    template<typename Func, typename... Args>
    auto async_HHBUI(Func&& func, Args&&... args) noexcept {
        try {
            return std::async(std::launch::async, std::forward<Func>(func), std::forward<Args>(args)...);
        } catch (...) {
            using ReturnType = decltype(func(args...));
            std::promise<ReturnType> promise;
            promise.set_exception(std::current_exception());
            return promise.get_future();
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIRenderThread = hhbui::modern::Thread_HHBUI;
    
    template<typename T>
    using UIQueue = hhbui::modern::ThreadSafeQueue_HHBUI<T>;
    
    using ThreadManager = hhbui::modern::ThreadPool_HHBUI;
}
