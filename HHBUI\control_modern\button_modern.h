/**
** =====================================================================================
**
**       文件名称: button_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化按钮控件 - 按钮控件 （声明文件）
**
**       主要功能:
**       - 现代化按钮控件与交互体验
**       - 高性能按钮渲染与动画效果
**       - 智能按钮样式与主题系统
**       - 异常安全的按钮操作接口
**       - 跨平台按钮抽象与兼容
**       - 按钮性能监控与调试支持
**       - 现代化按钮事件处理机制
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与异常安全保证
**       - 高性能事件处理与状态管理
**       - 模板元编程与编译时优化
**       - 多线程安全的按钮操作
**       - 标准库容器与算法集成
**       - 跨平台按钮抽象层设计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化按钮控件
**                             2. 实现智能事件处理机制
**                             3. 添加高性能渲染系统
**                             4. 集成异常安全保证机制
**                             5. 优化按钮交互性能
**                             6. 确保跨平台兼容性
**                             7. 添加按钮调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <functional>
#include <optional>
#include <chrono>
#include <atomic>
#include <mutex>
#include "../ui_modern/control_base_modern.h"

namespace hhbui::modern {

    // 前向声明
    class Image_HHBUI;
    class Brush_HHBUI;
    class Animation_HHBUI;

    /// 按钮类型枚举
    enum class ButtonType_HHBUI : uint32_t {
        Fill = 0,       // 填充类型（背景颜色填充）
        Plain = 1,      // 朴素类型（淡色背景+边框）
        Circle = 2,     // 图标类型（此类型仅显示图标）
        NoBkg = 3       // 无背景填充（此模式可用于基类自带背景颜色组合）
    };

    /// 按钮样式枚举
    enum class ButtonStyle_HHBUI : uint32_t {
        Normal = 0,     // 默认样式（白色+蓝色）
        Customize = 1,  // 自定义（自定义颜色仅此样式生效）
        Primary = 2,    // 重点样式（蓝色）
        Success = 3,    // 成功样式（绿色）
        Info = 4,       // 信息样式（灰色）
        Warning = 5,    // 警告样式（黄色）
        Danger = 6,     // 危险样式（红色）
        NoStyle = 7     // 无样式 取消背景填充
    };

    /// 图标位置枚举
    enum class IconPosition_HHBUI : uint32_t {
        Left = 0,       // 图标在左侧
        Top = 1,        // 图标在上方
        Right = 2,      // 图标在右侧
        Bottom = 3      // 图标在下方
    };

    /// 按钮配置结构
    struct ButtonConfig_HHBUI {
        // 文本颜色 [正常, 悬停, 按下]
        Array<UIColor_HHBUI, 3> textColors{
            UIColor_HHBUI{0.0f, 0.0f, 0.0f, 1.0f},     // 正常
            UIColor_HHBUI{0.0f, 0.0f, 0.0f, 1.0f},     // 悬停
            UIColor_HHBUI{0.0f, 0.0f, 0.0f, 1.0f}      // 按下
        };
        
        // 背景颜色 [正常, 悬停, 按下]
        Array<UIColor_HHBUI, 3> backgroundColors{
            UIColor_HHBUI{0.9f, 0.9f, 0.9f, 1.0f},     // 正常
            UIColor_HHBUI{0.8f, 0.8f, 0.8f, 1.0f},     // 悬停
            UIColor_HHBUI{0.7f, 0.7f, 0.7f, 1.0f}      // 按下
        };
        
        // 边框颜色 [正常, 悬停, 按下]
        Array<UIColor_HHBUI, 3> borderColors{
            UIColor_HHBUI{0.6f, 0.6f, 0.6f, 1.0f},     // 正常
            UIColor_HHBUI{0.5f, 0.5f, 0.5f, 1.0f},     // 悬停
            UIColor_HHBUI{0.4f, 0.4f, 0.4f, 1.0f}      // 按下
        };
        
        // 背景图像 [正常, 悬停, 按下]
        Array<SharedPtr<Image_HHBUI>, 3> backgroundImages{nullptr, nullptr, nullptr};
    };

    /// 按钮事件类型
    enum class ButtonEvent_HHBUI : uint32_t {
        Click = 0,
        DoubleClick = 1,
        MouseEnter = 2,
        MouseLeave = 3,
        MouseDown = 4,
        MouseUp = 5,
        KeyDown = 6,
        KeyUp = 7,
        FocusGained = 8,
        FocusLost = 9
    };

    /// 按钮事件处理器
    using ButtonEventHandler_HHBUI = Function<void(ButtonEvent_HHBUI, const Any&)>;

    /// 现代化按钮控件类
    class Button_HHBUI : public ControlBase_HHBUI {
    public:
        /// 构造函数
        explicit Button_HHBUI(StringView name = L"HHBUI_Button") noexcept;

        /// 析构函数
        ~Button_HHBUI() noexcept override;

        /// 初始化按钮
        [[nodiscard]] bool initialize_HHBUI(
            ControlBase_HHBUI* parent,
            const UIRect_HHBUI& bounds,
            StringView text = L"Button",
            ButtonType_HHBUI type = ButtonType_HHBUI::Fill,
            ButtonStyle_HHBUI style = ButtonStyle_HHBUI::Normal,
            int32_t id = 0
        ) noexcept;

        /// 设置按钮类型和样式
        void setButtonStyle_HHBUI(ButtonType_HHBUI type, ButtonStyle_HHBUI style) noexcept;

        /// 获取按钮类型
        [[nodiscard]] ButtonType_HHBUI getButtonType_HHBUI() const noexcept;

        /// 获取按钮样式
        [[nodiscard]] ButtonStyle_HHBUI getButtonStyle_HHBUI() const noexcept;

        /// 设置文本颜色
        void setTextColors_HHBUI(
            const UIColor_HHBUI& normal,
            const UIColor_HHBUI& hover,
            const UIColor_HHBUI& pressed
        ) noexcept;

        /// 设置背景颜色
        void setBackgroundColors_HHBUI(
            const UIColor_HHBUI& normal,
            const UIColor_HHBUI& hover,
            const UIColor_HHBUI& pressed
        ) noexcept;

        /// 设置边框颜色
        void setBorderColors_HHBUI(
            const UIColor_HHBUI& normal,
            const UIColor_HHBUI& hover,
            const UIColor_HHBUI& pressed
        ) noexcept;

        /// 设置背景图像
        void setBackgroundImages_HHBUI(
            SharedPtr<Image_HHBUI> normal,
            SharedPtr<Image_HHBUI> hover = nullptr,
            SharedPtr<Image_HHBUI> pressed = nullptr
        ) noexcept;

        /// 设置圆角半径
        void setRadius_HHBUI(float radius) noexcept;

        /// 获取圆角半径
        [[nodiscard]] float getRadius_HHBUI() const noexcept;

        /// 设置按钮图标
        void setIcon_HHBUI(SharedPtr<Image_HHBUI> icon, IconPosition_HHBUI position = IconPosition_HHBUI::Left) noexcept;

        /// 获取按钮图标
        [[nodiscard]] SharedPtr<Image_HHBUI> getIcon_HHBUI() const noexcept;

        /// 获取图标位置
        [[nodiscard]] IconPosition_HHBUI getIconPosition_HHBUI() const noexcept;

        /// 设置图标大小
        void setIconSize_HHBUI(const UISize_HHBUI& size) noexcept;

        /// 获取图标大小
        [[nodiscard]] UISize_HHBUI getIconSize_HHBUI() const noexcept;

        /// 设置文本与图标间距
        void setIconTextSpacing_HHBUI(float spacing) noexcept;

        /// 获取文本与图标间距
        [[nodiscard]] float getIconTextSpacing_HHBUI() const noexcept;

        /// 启用/禁用按钮动画
        void setAnimationEnabled_HHBUI(bool enabled) noexcept;

        /// 检查按钮动画是否启用
        [[nodiscard]] bool isAnimationEnabled_HHBUI() const noexcept;

        /// 设置动画持续时间
        void setAnimationDuration_HHBUI(std::chrono::milliseconds duration) noexcept;

        /// 获取动画持续时间
        [[nodiscard]] std::chrono::milliseconds getAnimationDuration_HHBUI() const noexcept;

        /// 设置按钮为默认按钮
        void setDefault_HHBUI(bool isDefault) noexcept;

        /// 检查是否为默认按钮
        [[nodiscard]] bool isDefault_HHBUI() const noexcept;

        /// 设置按钮为取消按钮
        void setCancel_HHBUI(bool isCancel) noexcept;

        /// 检查是否为取消按钮
        [[nodiscard]] bool isCancel_HHBUI() const noexcept;

        /// 模拟按钮点击
        void performClick_HHBUI() noexcept;

        /// 注册按钮事件处理器
        void registerEventHandler_HHBUI(ButtonEvent_HHBUI eventType, ButtonEventHandler_HHBUI handler) noexcept;

        /// 注销按钮事件处理器
        void unregisterEventHandler_HHBUI(ButtonEvent_HHBUI eventType) noexcept;

        /// 获取按钮配置
        [[nodiscard]] const ButtonConfig_HHBUI& getConfig_HHBUI() const noexcept;

        /// 设置按钮配置
        void setConfig_HHBUI(const ButtonConfig_HHBUI& config) noexcept;

        /// 应用预定义样式
        void applyPredefinedStyle_HHBUI(ButtonStyle_HHBUI style) noexcept;

        /// 重置为默认样式
        void resetToDefaultStyle_HHBUI() noexcept;

    protected:
        /// 消息处理函数重写
        LRESULT onMessage_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept override;

        /// 绘制函数重写
        void onPaint_HHBUI(Canvas_HHBUI* canvas) noexcept override;

        /// 状态变更通知重写
        void onStateChanged_HHBUI(ControlState_HHBUI oldState, ControlState_HHBUI newState) noexcept override;

        /// 鼠标进入事件
        virtual void onMouseEnter_HHBUI() noexcept;

        /// 鼠标离开事件
        virtual void onMouseLeave_HHBUI() noexcept;

        /// 鼠标按下事件
        virtual void onMouseDown_HHBUI(const UIPoint_HHBUI& position) noexcept;

        /// 鼠标释放事件
        virtual void onMouseUp_HHBUI(const UIPoint_HHBUI& position) noexcept;

        /// 点击事件
        virtual void onClick_HHBUI() noexcept;

        /// 双击事件
        virtual void onDoubleClick_HHBUI() noexcept;

        /// 键盘按下事件
        virtual void onKeyDown_HHBUI(uint32_t keyCode) noexcept;

        /// 键盘释放事件
        virtual void onKeyUp_HHBUI(uint32_t keyCode) noexcept;

        /// 焦点获得事件
        virtual void onFocusGained_HHBUI() noexcept;

        /// 焦点失去事件
        virtual void onFocusLost_HHBUI() noexcept;

    private:
        /// 绘制按钮背景
        void drawBackground_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制按钮边框
        void drawBorder_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制按钮文本
        void drawText_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 绘制按钮图标
        void drawIcon_HHBUI(Canvas_HHBUI* canvas, const UIRect_HHBUI& rect) noexcept;

        /// 计算文本矩形
        [[nodiscard]] UIRect_HHBUI calculateTextRect_HHBUI(const UIRect_HHBUI& clientRect) const noexcept;

        /// 计算图标矩形
        [[nodiscard]] UIRect_HHBUI calculateIconRect_HHBUI(const UIRect_HHBUI& clientRect) const noexcept;

        /// 获取当前状态的颜色
        [[nodiscard]] UIColor_HHBUI getCurrentTextColor_HHBUI() const noexcept;
        [[nodiscard]] UIColor_HHBUI getCurrentBackgroundColor_HHBUI() const noexcept;
        [[nodiscard]] UIColor_HHBUI getCurrentBorderColor_HHBUI() const noexcept;

        /// 获取当前状态的图像
        [[nodiscard]] SharedPtr<Image_HHBUI> getCurrentBackgroundImage_HHBUI() const noexcept;

        /// 触发按钮事件
        void triggerEvent_HHBUI(ButtonEvent_HHBUI eventType, const Any& eventData = {}) noexcept;

        /// 开始状态动画
        void startStateAnimation_HHBUI(ControlState_HHBUI targetState) noexcept;

        /// 停止状态动画
        void stopStateAnimation_HHBUI() noexcept;

        /// 更新按钮外观
        void updateAppearance_HHBUI() noexcept;

    private:
        ButtonType_HHBUI buttonType_{ButtonType_HHBUI::Fill};
        ButtonStyle_HHBUI buttonStyle_{ButtonStyle_HHBUI::Normal};
        ButtonConfig_HHBUI config_{};
        
        float radius_ = 8.0f;
        SharedPtr<Image_HHBUI> icon_{};
        IconPosition_HHBUI iconPosition_{IconPosition_HHBUI::Left};
        UISize_HHBUI iconSize_{16.0f, 16.0f};
        float iconTextSpacing_ = 4.0f;
        
        std::atomic<bool> animationEnabled_{true};
        std::chrono::milliseconds animationDuration_{200};
        UniquePtr<Animation_HHBUI> stateAnimation_{};
        
        std::atomic<bool> isDefault_{false};
        std::atomic<bool> isCancel_{false};
        
        HashMap<ButtonEvent_HHBUI, Vector<ButtonEventHandler_HHBUI>> eventHandlers_{};
        mutable std::shared_mutex eventHandlersMutex_{};
        
        UniquePtr<Brush_HHBUI> backgroundBrush_{};
        UniquePtr<Brush_HHBUI> borderBrush_{};
        
        std::chrono::steady_clock::time_point lastClickTime_{};
        UIPoint_HHBUI lastMousePosition_{};
    };

    /// 全局按钮函数

    /// 创建按钮
    [[nodiscard]] UniquePtr<Button_HHBUI> createButton_HHBUI(
        StringView text = L"Button",
        ButtonType_HHBUI type = ButtonType_HHBUI::Fill,
        ButtonStyle_HHBUI style = ButtonStyle_HHBUI::Normal
    ) noexcept;

    /// 创建图标按钮
    [[nodiscard]] UniquePtr<Button_HHBUI> createIconButton_HHBUI(
        SharedPtr<Image_HHBUI> icon,
        StringView text = L"",
        IconPosition_HHBUI position = IconPosition_HHBUI::Left
    ) noexcept;

    /// 创建预定义样式按钮
    [[nodiscard]] UniquePtr<Button_HHBUI> createStyledButton_HHBUI(
        StringView text,
        ButtonStyle_HHBUI style
    ) noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIButton = hhbui::modern::Button_HHBUI;
    using info_button_config = hhbui::modern::ButtonConfig_HHBUI;
    using info_button_style = hhbui::modern::ButtonStyle_HHBUI;
    using info_button_type = hhbui::modern::ButtonType_HHBUI;
}
