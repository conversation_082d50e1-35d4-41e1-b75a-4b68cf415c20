/**
** =====================================================================================
**
**       文件名称: window_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化窗口管理系统 - 窗口管理 （声明文件）
**
**       主要功能:
**       - 现代化窗口创建与生命周期管理
**       - 高性能窗口消息处理与事件分发
**       - 智能窗口布局与自适应调整
**       - 异常安全的窗口操作接口
**       - 跨平台窗口抽象与兼容
**       - 窗口性能监控与调试支持
**       - 现代化窗口样式与主题系统
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与异常安全保证
**       - 高性能消息处理与事件队列
**       - 模板元编程与编译时优化
**       - 多线程安全的窗口操作
**       - 标准库容器与算法集成
**       - 跨平台窗口抽象层设计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化窗口管理系统
**                             2. 实现智能消息处理机制
**                             3. 添加高性能布局系统
**                             4. 集成异常安全保证机制
**                             5. 优化窗口渲染性能
**                             6. 确保跨平台兼容性
**                             7. 添加窗口调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_map>
#include <optional>
#include <functional>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <Windows.h>
#include "control_base_modern.h"

namespace hhbui::modern {

    // 前向声明
    class Canvas_HHBUI;
    class Layout_HHBUI;
    class ResourceManager_HHBUI;

    /// 窗口样式枚举
    enum class WindowStyle_HHBUI : uint32_t {
        None = 0,
        Overlapped = WS_OVERLAPPED,
        Popup = WS_POPUP,
        Child = WS_CHILD,
        Minimize = WS_MINIMIZE,
        Visible = WS_VISIBLE,
        Disabled = WS_DISABLED,
        ClipSiblings = WS_CLIPSIBLINGS,
        ClipChildren = WS_CLIPCHILDREN,
        Maximize = WS_MAXIMIZE,
        Caption = WS_CAPTION,
        Border = WS_BORDER,
        DialogFrame = WS_DLGFRAME,
        VScroll = WS_VSCROLL,
        HScroll = WS_HSCROLL,
        SysMenu = WS_SYSMENU,
        ThickFrame = WS_THICKFRAME,
        Group = WS_GROUP,
        TabStop = WS_TABSTOP,
        MinimizeBox = WS_MINIMIZEBOX,
        MaximizeBox = WS_MAXIMIZEBOX,
        OverlappedWindow = WS_OVERLAPPEDWINDOW,
        PopupWindow = WS_POPUPWINDOW
    };

    /// 窗口扩展样式枚举
    enum class WindowStyleEx_HHBUI : uint32_t {
        None = 0,
        DialogModalFrame = WS_EX_DLGMODALFRAME,
        NoParentNotify = WS_EX_NOPARENTNOTIFY,
        TopMost = WS_EX_TOPMOST,
        AcceptFiles = WS_EX_ACCEPTFILES,
        Transparent = WS_EX_TRANSPARENT,
        MDIChild = WS_EX_MDICHILD,
        ToolWindow = WS_EX_TOOLWINDOW,
        WindowEdge = WS_EX_WINDOWEDGE,
        ClientEdge = WS_EX_CLIENTEDGE,
        ContextHelp = WS_EX_CONTEXTHELP,
        Right = WS_EX_RIGHT,
        Left = WS_EX_LEFT,
        RTLReading = WS_EX_RTLREADING,
        LTRReading = WS_EX_LTRREADING,
        LeftScrollBar = WS_EX_LEFTSCROLLBAR,
        RightScrollBar = WS_EX_RIGHTSCROLLBAR,
        ControlParent = WS_EX_CONTROLPARENT,
        StaticEdge = WS_EX_STATICEDGE,
        AppWindow = WS_EX_APPWINDOW,
        Layered = WS_EX_LAYERED,
        NoInheritLayout = WS_EX_NOINHERITLAYOUT,
        NoRedirectionBitmap = WS_EX_NOREDIRECTIONBITMAP,
        LayoutRTL = WS_EX_LAYOUTRTL,
        Composited = WS_EX_COMPOSITED,
        NoActivate = WS_EX_NOACTIVATE
    };

    /// UI窗口样式枚举
    enum class UIWindowStyle_HHBUI : uint32_t {
        None = 0,
        CenterWindow = 1,
        FullScreen = 2,
        Borderless = 4,
        Resizable = 8,
        Minimizable = 16,
        Maximizable = 32,
        Closable = 64,
        AlwaysOnTop = 128,
        ToolWindow = 256,
        Dialog = 512
    };

    /// 窗口状态枚举
    enum class WindowState_HHBUI : uint32_t {
        Normal = 0,
        Minimized = 1,
        Maximized = 2,
        Hidden = 3,
        Closed = 4
    };

    /// 窗口创建信息
    struct WindowCreateInfo_HHBUI {
        String title = L"HHBUI Window";
        String className = L"HHBUI_WindowClass";
        UIRect_HHBUI bounds{100, 100, 800, 600};
        WindowStyle_HHBUI style = WindowStyle_HHBUI::OverlappedWindow;
        WindowStyleEx_HHBUI styleEx = WindowStyleEx_HHBUI::None;
        UIWindowStyle_HHBUI uiStyle = UIWindowStyle_HHBUI::CenterWindow;
        HWND parentWindow = nullptr;
        HICON icon = nullptr;
        HICON smallIcon = nullptr;
        HCURSOR cursor = nullptr;
        HBRUSH background = nullptr;
        LPARAM userData = 0;
        bool visible = true;
        bool enabled = true;
    };

    /// 现代化窗口类
    class Window_HHBUI : public ControlBase_HHBUI {
    public:
        /// 构造函数
        explicit Window_HHBUI(StringView name = L"HHBUI_Window") noexcept;

        /// 析构函数
        ~Window_HHBUI() noexcept override;

        /// 创建窗口
        [[nodiscard]] bool create_HHBUI(const WindowCreateInfo_HHBUI& createInfo) noexcept;

        /// 从现有窗口句柄创建
        [[nodiscard]] bool createFromHandle_HHBUI(HWND hwnd, UIWindowStyle_HHBUI uiStyle = UIWindowStyle_HHBUI::None) noexcept;

        /// 从XML创建窗口界面
        [[nodiscard]] bool createFromXML_HHBUI(
            HWND parentWindow,
            ResourceManager_HHBUI* resourceManager,
            StringView xmlContent,
            EventHandler_HHBUI messageHandler = nullptr
        ) noexcept;

        /// 保存窗口界面为XML
        [[nodiscard]] bool saveToXML_HHBUI(StringView filePath) const noexcept;

        /// 销毁窗口
        void destroy_HHBUI() noexcept override;

        /// 显示窗口
        void show_HHBUI(bool visible = true) noexcept override;

        /// 隐藏窗口
        void hide_HHBUI() noexcept override;

        /// 最小化窗口
        void minimize_HHBUI() noexcept;

        /// 最大化窗口
        void maximize_HHBUI() noexcept;

        /// 还原窗口
        void restore_HHBUI() noexcept;

        /// 居中窗口
        void center_HHBUI() noexcept;

        /// 置顶窗口
        void bringToTop_HHBUI() noexcept;

        /// 发送消息
        [[nodiscard]] LRESULT sendMessage_HHBUI(UINT message, WPARAM wParam = 0, LPARAM lParam = 0) noexcept;

        /// 投递消息
        [[nodiscard]] bool postMessage_HHBUI(UINT message, WPARAM wParam = 0, LPARAM lParam = 0) noexcept;

        /// 获取窗口句柄
        [[nodiscard]] HWND getHandle_HHBUI() const noexcept;

        /// 获取父窗口句柄
        [[nodiscard]] HWND getParentHandle_HHBUI() const noexcept;

        /// 获取窗口实例
        [[nodiscard]] Window_HHBUI* getInstance_HHBUI() const noexcept;

        /// 获取客户区矩形
        [[nodiscard]] UIRect_HHBUI getClientRect_HHBUI() const noexcept;

        /// 获取窗口矩形
        [[nodiscard]] UIRect_HHBUI getWindowRect_HHBUI() const noexcept;

        /// 设置窗口标题
        void setTitle_HHBUI(StringView title) noexcept;

        /// 获取窗口标题
        [[nodiscard]] String getTitle_HHBUI() const noexcept;

        /// 设置窗口图标
        void setIcon_HHBUI(HICON icon, bool small = false) noexcept;

        /// 获取窗口图标
        [[nodiscard]] HICON getIcon_HHBUI(bool small = false) const noexcept;

        /// 设置窗口状态
        void setWindowState_HHBUI(WindowState_HHBUI state) noexcept;

        /// 获取窗口状态
        [[nodiscard]] WindowState_HHBUI getWindowState_HHBUI() const noexcept;

        /// 设置窗口透明度
        void setOpacity_HHBUI(float opacity) noexcept;

        /// 获取窗口透明度
        [[nodiscard]] float getOpacity_HHBUI() const noexcept;

        /// 设置窗口层级
        void setZOrder_HHBUI(HWND insertAfter) noexcept;

        /// 启用/禁用窗口拖拽
        void setDragEnabled_HHBUI(bool enabled) noexcept;

        /// 检查窗口拖拽是否启用
        [[nodiscard]] bool isDragEnabled_HHBUI() const noexcept;

        /// 启用/禁用窗口调整大小
        void setResizeEnabled_HHBUI(bool enabled) noexcept;

        /// 检查窗口调整大小是否启用
        [[nodiscard]] bool isResizeEnabled_HHBUI() const noexcept;

        /// 设置最小尺寸
        void setMinSize_HHBUI(const UISize_HHBUI& minSize) noexcept;

        /// 获取最小尺寸
        [[nodiscard]] UISize_HHBUI getMinSize_HHBUI() const noexcept;

        /// 设置最大尺寸
        void setMaxSize_HHBUI(const UISize_HHBUI& maxSize) noexcept;

        /// 获取最大尺寸
        [[nodiscard]] UISize_HHBUI getMaxSize_HHBUI() const noexcept;

        /// 获取用户数据
        [[nodiscard]] LPARAM getUserData_HHBUI() const noexcept;

        /// 设置用户数据
        void setUserData_HHBUI(LPARAM userData) noexcept;

        /// 获取功能键状态
        [[nodiscard]] uint32_t getKeyState_HHBUI() const noexcept;

        /// 设置标志位
        void setFlag_HHBUI(uint32_t flag, bool value) noexcept;

        /// 获取标志位
        [[nodiscard]] bool getFlag_HHBUI(uint32_t flag) const noexcept;

        /// 从窗口句柄获取窗口对象
        [[nodiscard]] static Window_HHBUI* fromHandle_HHBUI(HWND hwnd) noexcept;

        /// 注册窗口类
        [[nodiscard]] static bool registerWindowClass_HHBUI(
            StringView className,
            HICON icon = nullptr,
            HICON smallIcon = nullptr,
            HCURSOR cursor = nullptr,
            HBRUSH background = nullptr
        ) noexcept;

        /// 注销窗口类
        static void unregisterWindowClass_HHBUI(StringView className) noexcept;

        /// 运行消息循环
        [[nodiscard]] static int32_t runMessageLoop_HHBUI() noexcept;

        /// 退出消息循环
        static void exitMessageLoop_HHBUI(int32_t exitCode = 0) noexcept;

        /// 处理单个消息
        [[nodiscard]] static bool processMessage_HHBUI() noexcept;

    protected:
        /// 窗口过程函数
        virtual LRESULT windowProc_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept;

        /// 创建通知
        virtual void onCreate_HHBUI() noexcept;

        /// 销毁通知
        virtual void onDestroy_HHBUI() noexcept;

        /// 关闭通知
        virtual bool onClose_HHBUI() noexcept;

        /// 激活通知
        virtual void onActivate_HHBUI(bool activated) noexcept;

        /// 尺寸变更通知
        virtual void onSize_HHBUI(uint32_t type, const UISize_HHBUI& newSize) noexcept;

        /// 移动通知
        virtual void onMove_HHBUI(const UIPoint_HHBUI& newPosition) noexcept;

        /// 显示状态变更通知
        virtual void onShowWindow_HHBUI(bool shown, uint32_t status) noexcept;

        /// DPI变更通知
        virtual void onDpiChanged_HHBUI(uint32_t dpi, const UIRect_HHBUI& suggestedRect) noexcept;

    private:
        /// 内部窗口过程
        static LRESULT CALLBACK internalWindowProc_HHBUI(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) noexcept;

        /// 初始化窗口
        [[nodiscard]] bool internalInitialize_HHBUI(const WindowCreateInfo_HHBUI& createInfo) noexcept;

        /// 清理窗口
        void internalCleanup_HHBUI() noexcept;

        /// 更新窗口状态
        void updateWindowState_HHBUI() noexcept;

    private:
        HWND hwnd_ = nullptr;
        HWND parentHwnd_ = nullptr;
        WindowCreateInfo_HHBUI createInfo_{};
        std::atomic<WindowState_HHBUI> windowState_{WindowState_HHBUI::Normal};
        
        UISize_HHBUI minSize_{100, 100};
        UISize_HHBUI maxSize_{32767, 32767};
        float opacity_ = 1.0f;
        
        std::atomic<bool> dragEnabled_{true};
        std::atomic<bool> resizeEnabled_{true};
        std::atomic<uint32_t> flags_{0};
        LPARAM userData_ = 0;
        
        UniquePtr<Canvas_HHBUI> canvas_{};
        UniquePtr<ResourceManager_HHBUI> resourceManager_{};
        
        static inline HashMap<HWND, Window_HHBUI*> windowMap_{};
        static inline std::shared_mutex windowMapMutex_{};
    };

    /// 全局窗口管理函数

    /// 创建窗口
    [[nodiscard]] UniquePtr<Window_HHBUI> createWindow_HHBUI(const WindowCreateInfo_HHBUI& createInfo) noexcept;

    /// 查找窗口
    [[nodiscard]] Window_HHBUI* findWindow_HHBUI(HWND hwnd) noexcept;
    [[nodiscard]] Window_HHBUI* findWindow_HHBUI(StringView title) noexcept;

    /// 枚举所有窗口
    void enumerateWindows_HHBUI(Function<bool(Window_HHBUI*)> callback) noexcept;

    /// 获取活动窗口
    [[nodiscard]] Window_HHBUI* getActiveWindow_HHBUI() noexcept;

    /// 获取前台窗口
    [[nodiscard]] Window_HHBUI* getForegroundWindow_HHBUI() noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using UIWnd = hhbui::modern::Window_HHBUI;
    using WindowCreateInfo = hhbui::modern::WindowCreateInfo_HHBUI;
    using WindowState = hhbui::modern::WindowState_HHBUI;
}
