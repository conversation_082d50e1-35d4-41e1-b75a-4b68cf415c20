/**
** =====================================================================================
**
**       文件名称: string_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化字符串处理工具 - 字符串工具 （声明文件）
**
**       主要功能:
**       - 现代化Unicode字符串处理与转换
**       - 高性能字符串操作与算法优化
**       - 智能字符编码检测与转换
**       - 异常安全的字符串处理接口
**       - 跨平台字符串格式化与本地化
**       - 正则表达式与模式匹配支持
**       - 字符串池与内存优化管理
**
**       技术特性:
**       - 采用现代C++17标准与string_view优化
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能SIMD字符串算法优化
**       - 模板元编程与编译时优化
**       - 标准库算法与容器集成
**       - 跨平台Unicode标准支持
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化字符串处理工具
**                             2. 实现高性能Unicode转换
**                             3. 添加智能编码检测机制
**                             4. 集成异常安全保证机制
**                             5. 优化字符串算法性能
**                             6. 确保跨平台兼容性
**                             7. 添加正则表达式支持
**
** =====================================================================================
**/

#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <array>
#include <optional>
#include <variant>
#include <algorithm>
#include <functional>
#include <regex>
#include <locale>
#include <codecvt>
#include <format>
#include <charconv>
#include <type_traits>
#include <Windows.h>

namespace hhbui::modern {

    /// 字符编码枚举
    enum class StringEncoding_HHBUI : uint32_t {
        UTF8 = 0,
        UTF16LE = 1,
        UTF16BE = 2,
        UTF32LE = 3,
        UTF32BE = 4,
        ANSI = 5,
        ASCII = 6,
        GBK = 7,
        GB2312 = 8,
        BIG5 = 9
    };

    /// 字符串比较选项
    enum class StringCompareOptions_HHBUI : uint32_t {
        None = 0,
        IgnoreCase = 1,
        IgnoreWhitespace = 2,
        IgnoreSymbols = 4,
        Numeric = 8,
        Locale = 16
    };

    /// 字符串分割选项
    enum class StringSplitOptions_HHBUI : uint32_t {
        None = 0,
        RemoveEmptyEntries = 1,
        TrimEntries = 2,
        IgnoreCase = 4
    };

    /// 现代化字符串工具类
    class StringUtils_HHBUI {
    public:
        /// 删除构造函数（静态工具类）
        StringUtils_HHBUI() = delete;
        ~StringUtils_HHBUI() = delete;
        StringUtils_HHBUI(const StringUtils_HHBUI&) = delete;
        StringUtils_HHBUI& operator=(const StringUtils_HHBUI&) = delete;

        // ========== 字符串转换 ==========

        /// UTF-8 到 UTF-16 转换
        [[nodiscard]] static Optional<String> utf8ToUtf16_HHBUI(StringViewU8 utf8Str) noexcept;

        /// UTF-16 到 UTF-8 转换
        [[nodiscard]] static Optional<StringU8> utf16ToUtf8_HHBUI(StringView utf16Str) noexcept;

        /// ANSI 到 UTF-16 转换
        [[nodiscard]] static Optional<String> ansiToUtf16_HHBUI(StringViewU8 ansiStr, uint32_t codePage = CP_ACP) noexcept;

        /// UTF-16 到 ANSI 转换
        [[nodiscard]] static Optional<StringU8> utf16ToAnsi_HHBUI(StringView utf16Str, uint32_t codePage = CP_ACP) noexcept;

        /// 通用编码转换
        [[nodiscard]] static Optional<String> convertEncoding_HHBUI(
            StringViewU8 sourceStr,
            StringEncoding_HHBUI sourceEncoding,
            StringEncoding_HHBUI targetEncoding
        ) noexcept;

        /// 自动检测字符编码
        [[nodiscard]] static StringEncoding_HHBUI detectEncoding_HHBUI(StringViewU8 data) noexcept;

        // ========== 字符串操作 ==========

        /// 去除左侧空白字符
        [[nodiscard]] static String trimLeft_HHBUI(StringView str) noexcept;

        /// 去除右侧空白字符
        [[nodiscard]] static String trimRight_HHBUI(StringView str) noexcept;

        /// 去除两侧空白字符
        [[nodiscard]] static String trim_HHBUI(StringView str) noexcept;

        /// 去除指定字符
        [[nodiscard]] static String trimChars_HHBUI(StringView str, StringView chars) noexcept;

        /// 转换为大写
        [[nodiscard]] static String toUpper_HHBUI(StringView str) noexcept;

        /// 转换为小写
        [[nodiscard]] static String toLower_HHBUI(StringView str) noexcept;

        /// 转换为标题格式（首字母大写）
        [[nodiscard]] static String toTitle_HHBUI(StringView str) noexcept;

        /// 反转字符串
        [[nodiscard]] static String reverse_HHBUI(StringView str) noexcept;

        /// 重复字符串
        [[nodiscard]] static String repeat_HHBUI(StringView str, size_t count) noexcept;

        /// 填充字符串（左侧）
        [[nodiscard]] static String padLeft_HHBUI(StringView str, size_t totalWidth, wchar_t paddingChar = L' ') noexcept;

        /// 填充字符串（右侧）
        [[nodiscard]] static String padRight_HHBUI(StringView str, size_t totalWidth, wchar_t paddingChar = L' ') noexcept;

        /// 居中填充字符串
        [[nodiscard]] static String padCenter_HHBUI(StringView str, size_t totalWidth, wchar_t paddingChar = L' ') noexcept;

        // ========== 字符串查找与替换 ==========

        /// 查找子字符串位置
        [[nodiscard]] static Optional<size_t> indexOf_HHBUI(
            StringView str,
            StringView subStr,
            size_t startPos = 0,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 查找子字符串最后位置
        [[nodiscard]] static Optional<size_t> lastIndexOf_HHBUI(
            StringView str,
            StringView subStr,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 查找所有子字符串位置
        [[nodiscard]] static Vector<size_t> findAll_HHBUI(
            StringView str,
            StringView subStr,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 检查是否包含子字符串
        [[nodiscard]] static bool contains_HHBUI(
            StringView str,
            StringView subStr,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 检查是否以指定字符串开始
        [[nodiscard]] static bool startsWith_HHBUI(
            StringView str,
            StringView prefix,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 检查是否以指定字符串结束
        [[nodiscard]] static bool endsWith_HHBUI(
            StringView str,
            StringView suffix,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 替换所有匹配的子字符串
        [[nodiscard]] static String replaceAll_HHBUI(
            StringView str,
            StringView oldValue,
            StringView newValue,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 替换第一个匹配的子字符串
        [[nodiscard]] static String replaceFirst_HHBUI(
            StringView str,
            StringView oldValue,
            StringView newValue,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 替换最后一个匹配的子字符串
        [[nodiscard]] static String replaceLast_HHBUI(
            StringView str,
            StringView oldValue,
            StringView newValue,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        // ========== 字符串分割与连接 ==========

        /// 分割字符串
        [[nodiscard]] static Vector<String> split_HHBUI(
            StringView str,
            StringView delimiter,
            StringSplitOptions_HHBUI options = StringSplitOptions_HHBUI::None
        ) noexcept;

        /// 按字符分割字符串
        [[nodiscard]] static Vector<String> splitByChars_HHBUI(
            StringView str,
            StringView delimiters,
            StringSplitOptions_HHBUI options = StringSplitOptions_HHBUI::None
        ) noexcept;

        /// 按行分割字符串
        [[nodiscard]] static Vector<String> splitLines_HHBUI(
            StringView str,
            StringSplitOptions_HHBUI options = StringSplitOptions_HHBUI::None
        ) noexcept;

        /// 连接字符串数组
        [[nodiscard]] static String join_HHBUI(
            const Vector<String>& strings,
            StringView separator = L""
        ) noexcept;

        /// 连接字符串数组（带格式化）
        template<typename... Args>
        [[nodiscard]] static String joinFormat_HHBUI(
            const Vector<String>& strings,
            StringView separator,
            StringView format,
            Args&&... args
        ) noexcept;

        // ========== 字符串比较 ==========

        /// 比较字符串
        [[nodiscard]] static int32_t compare_HHBUI(
            StringView str1,
            StringView str2,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 检查字符串相等
        [[nodiscard]] static bool equals_HHBUI(
            StringView str1,
            StringView str2,
            StringCompareOptions_HHBUI options = StringCompareOptions_HHBUI::None
        ) noexcept;

        /// 自然排序比较（数字感知）
        [[nodiscard]] static int32_t naturalCompare_HHBUI(StringView str1, StringView str2) noexcept;

        // ========== 字符串格式化 ==========

        /// 现代化格式化字符串（C++20 std::format风格）
        template<typename... Args>
        [[nodiscard]] static String format_HHBUI(StringView formatStr, Args&&... args) noexcept;

        /// 安全格式化字符串（传统printf风格）
        [[nodiscard]] static String formatSafe_HHBUI(StringView formatStr, ...) noexcept;

        /// 格式化字符串（va_list版本）
        [[nodiscard]] static String formatVa_HHBUI(StringView formatStr, va_list args) noexcept;

        // ========== 字符串验证 ==========

        /// 检查是否为空或仅包含空白字符
        [[nodiscard]] static bool isNullOrWhitespace_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的数字
        [[nodiscard]] static bool isNumeric_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的整数
        [[nodiscard]] static bool isInteger_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的浮点数
        [[nodiscard]] static bool isFloat_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的电子邮件地址
        [[nodiscard]] static bool isEmail_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的URL
        [[nodiscard]] static bool isUrl_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的IPv4地址
        [[nodiscard]] static bool isIPv4_HHBUI(StringView str) noexcept;

        /// 检查是否为有效的IPv6地址
        [[nodiscard]] static bool isIPv6_HHBUI(StringView str) noexcept;

        // ========== 类型转换 ==========

        /// 字符串转整数
        template<typename T>
        [[nodiscard]] static Optional<T> toInteger_HHBUI(StringView str) noexcept;

        /// 字符串转浮点数
        template<typename T>
        [[nodiscard]] static Optional<T> toFloat_HHBUI(StringView str) noexcept;

        /// 字符串转布尔值
        [[nodiscard]] static Optional<bool> toBool_HHBUI(StringView str) noexcept;

        /// 数值转字符串
        template<typename T>
        [[nodiscard]] static String toString_HHBUI(T value) noexcept;

        // ========== 正则表达式 ==========

        /// 正则表达式匹配
        [[nodiscard]] static bool regexMatch_HHBUI(
            StringView str,
            StringView pattern,
            std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript
        ) noexcept;

        /// 正则表达式查找
        [[nodiscard]] static Vector<String> regexFind_HHBUI(
            StringView str,
            StringView pattern,
            std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript
        ) noexcept;

        /// 正则表达式替换
        [[nodiscard]] static String regexReplace_HHBUI(
            StringView str,
            StringView pattern,
            StringView replacement,
            std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript
        ) noexcept;

        // ========== 实用工具 ==========

        /// 生成随机字符串
        [[nodiscard]] static String generateRandom_HHBUI(
            size_t length,
            StringView charset = L"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        ) noexcept;

        /// 计算字符串哈希值
        [[nodiscard]] static size_t hash_HHBUI(StringView str) noexcept;

        /// 计算字符串的Levenshtein距离
        [[nodiscard]] static size_t levenshteinDistance_HHBUI(StringView str1, StringView str2) noexcept;

        /// 计算字符串相似度（0.0-1.0）
        [[nodiscard]] static double similarity_HHBUI(StringView str1, StringView str2) noexcept;

    private:
        /// 内部辅助函数
        static bool isWhitespace_HHBUI(wchar_t ch) noexcept;
        static std::wregex createRegex_HHBUI(StringView pattern, std::regex_constants::syntax_option_type flags) noexcept;
    };

    // ========== 模板实现 ==========

    template<typename... Args>
    String StringUtils_HHBUI::format_HHBUI(StringView formatStr, Args&&... args) noexcept {
        try {
            if constexpr (sizeof...(args) == 0) {
                return String(formatStr);
            } else {
                return std::format(formatStr, std::forward<Args>(args)...);
            }
        } catch (...) {
            return String(formatStr);  // 返回原始格式字符串作为后备
        }
    }

    template<typename T>
    Optional<T> StringUtils_HHBUI::toInteger_HHBUI(StringView str) noexcept {
        static_assert(std::is_integral_v<T>, "T must be an integral type");
        
        try {
            T result;
            auto [ptr, ec] = std::from_chars(
                reinterpret_cast<const char*>(str.data()),
                reinterpret_cast<const char*>(str.data() + str.size() * sizeof(wchar_t)),
                result
            );
            
            if (ec == std::errc{}) {
                return result;
            }
        } catch (...) {
            // 忽略异常
        }
        
        return std::nullopt;
    }

    template<typename T>
    Optional<T> StringUtils_HHBUI::toFloat_HHBUI(StringView str) noexcept {
        static_assert(std::is_floating_point_v<T>, "T must be a floating point type");
        
        try {
            T result;
            auto [ptr, ec] = std::from_chars(
                reinterpret_cast<const char*>(str.data()),
                reinterpret_cast<const char*>(str.data() + str.size() * sizeof(wchar_t)),
                result
            );
            
            if (ec == std::errc{}) {
                return result;
            }
        } catch (...) {
            // 忽略异常
        }
        
        return std::nullopt;
    }

    template<typename T>
    String StringUtils_HHBUI::toString_HHBUI(T value) noexcept {
        try {
            if constexpr (std::is_arithmetic_v<T>) {
                return std::to_wstring(value);
            } else if constexpr (std::is_convertible_v<T, String>) {
                return String(value);
            } else {
                return L"[object]";
            }
        } catch (...) {
            return L"[error]";
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using vstring = hhbui::modern::StringUtils_HHBUI;
}
