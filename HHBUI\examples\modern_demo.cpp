/**
** =====================================================================================
**
**       文件名称: modern_demo.cpp
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化框架演示程序 - 示例程序 （实现文件）
**
**       主要功能:
**       - 展示现代化HHBUI框架的使用方法
**       - 演示各种现代化UI控件的功能
**       - 展示现代化事件处理机制
**       - 演示异常安全的编程模式
**       - 展示跨平台兼容性特性
**       - 演示性能监控与调试功能
**       - 展示向后兼容性支持
**
**       技术特性:
**       - 采用现代C++17标准与最佳实践
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能事件处理与UI渲染
**       - 模板元编程与编译时优化
**       - 标准库容器与算法集成
**       - 跨平台UI应用程序架构
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化框架演示程序
**                             2. 实现各种UI控件演示
**                             3. 添加事件处理演示
**                             4. 集成异常安全演示
**                             5. 优化性能演示代码
**                             6. 确保跨平台兼容性
**                             7. 添加调试功能演示
**
** =====================================================================================
**/

#include "../hhbui_modern.h"
#include <iostream>
#include <memory>
#include <string>

using namespace hhbui::modern;

/// 现代化演示应用程序类
class ModernDemoApp_HHBUI {
public:
    /// 构造函数
    ModernDemoApp_HHBUI() noexcept = default;

    /// 析构函数
    ~ModernDemoApp_HHBUI() noexcept = default;

    /// 初始化应用程序
    [[nodiscard]] bool initialize_HHBUI() noexcept {
        try {
            // 配置应用程序初始化信息
            ApplicationInitInfo_HHBUI initInfo{};
            initInfo.instanceHandle = GetModuleHandle(nullptr);
            initInfo.applicationName = L"HHBUI Modern Demo";
            initInfo.applicationVersion = L"2.0.0.0";
            initInfo.debugMode = true;
            initInfo.enablePerformanceMonitoring = true;
            initInfo.defaultFontFace = L"Segoe UI";
            initInfo.defaultFontSize = 14;

            // 初始化现代化框架
            if (!initializeFramework_HHBUI(initInfo)) {
                std::wcerr << L"Failed to initialize HHBUI modern framework!" << std::endl;
                return false;
            }

            // 创建主窗口
            if (!createMainWindow_HHBUI()) {
                std::wcerr << L"Failed to create main window!" << std::endl;
                return false;
            }

            // 创建UI控件
            if (!createControls_HHBUI()) {
                std::wcerr << L"Failed to create UI controls!" << std::endl;
                return false;
            }

            // 设置事件处理器
            setupEventHandlers_HHBUI();

            std::wcout << L"HHBUI Modern Demo initialized successfully!" << std::endl;
            return true;

        } catch (const Exception_HHBUI& e) {
            std::wcerr << L"HHBUI Exception: " << e.format_HHBUI() << std::endl;
            return false;
        } catch (const std::exception& e) {
            std::wcerr << L"Standard Exception: " << StringUtils_HHBUI::utf8ToUtf16_HHBUI(e.what()).value_or(L"Unknown error") << std::endl;
            return false;
        } catch (...) {
            std::wcerr << L"Unknown exception occurred during initialization!" << std::endl;
            return false;
        }
    }

    /// 运行应用程序
    [[nodiscard]] int32_t run_HHBUI() noexcept {
        try {
            if (!mainWindow_) {
                std::wcerr << L"Main window not created!" << std::endl;
                return -1;
            }

            // 显示主窗口
            mainWindow_->show_HHBUI();
            mainWindow_->center_HHBUI();

            // 运行消息循环
            return runApplication_HHBUI();

        } catch (const Exception_HHBUI& e) {
            std::wcerr << L"HHBUI Exception during run: " << e.format_HHBUI() << std::endl;
            return -1;
        } catch (const std::exception& e) {
            std::wcerr << L"Standard Exception during run: " << StringUtils_HHBUI::utf8ToUtf16_HHBUI(e.what()).value_or(L"Unknown error") << std::endl;
            return -1;
        } catch (...) {
            std::wcerr << L"Unknown exception occurred during run!" << std::endl;
            return -1;
        }
    }

    /// 关闭应用程序
    void shutdown_HHBUI() noexcept {
        try {
            // 清理资源
            demoButton_.reset();
            demoEdit_.reset();
            mainWindow_.reset();

            // 关闭框架
            shutdownFramework_HHBUI();

            std::wcout << L"HHBUI Modern Demo shutdown successfully!" << std::endl;

        } catch (...) {
            std::wcerr << L"Exception occurred during shutdown!" << std::endl;
        }
    }

private:
    /// 创建主窗口
    [[nodiscard]] bool createMainWindow_HHBUI() noexcept {
        try {
            // 配置窗口创建信息
            WindowCreateInfo_HHBUI createInfo{};
            createInfo.title = L"HHBUI Modern Framework Demo";
            createInfo.bounds = UIRect_HHBUI{100, 100, 900, 700};
            createInfo.style = WindowStyle_HHBUI::OverlappedWindow;
            createInfo.uiStyle = UIWindowStyle_HHBUI::CenterWindow | UIWindowStyle_HHBUI::Resizable;
            createInfo.visible = false; // 稍后显示

            // 创建窗口
            mainWindow_ = createWindow_HHBUI(createInfo);
            if (!mainWindow_) {
                return false;
            }

            // 设置窗口最小尺寸
            mainWindow_->setMinSize_HHBUI(UISize_HHBUI{600, 400});

            return true;

        } catch (...) {
            return false;
        }
    }

    /// 创建UI控件
    [[nodiscard]] bool createControls_HHBUI() noexcept {
        try {
            if (!mainWindow_) {
                return false;
            }

            // 创建演示按钮
            demoButton_ = createStyledButton_HHBUI(L"Click Me!", ButtonStyle_HHBUI::Primary);
            if (!demoButton_) {
                return false;
            }

            // 初始化按钮
            if (!demoButton_->initialize_HHBUI(
                mainWindow_.get(),
                UIRect_HHBUI{50, 50, 200, 100},
                L"Demo Button",
                ButtonType_HHBUI::Fill,
                ButtonStyle_HHBUI::Primary,
                1001
            )) {
                return false;
            }

            // 创建演示编辑框
            demoEdit_ = createEdit_HHBUI(EditType_HHBUI::SingleLine, L"Enter text here...");
            if (!demoEdit_) {
                return false;
            }

            // 初始化编辑框
            if (!demoEdit_->initialize_HHBUI(
                mainWindow_.get(),
                UIRect_HHBUI{50, 120, 400, 160},
                EditType_HHBUI::SingleLine,
                L"Enter your text here...",
                1002
            )) {
                return false;
            }

            // 添加控件到窗口
            mainWindow_->addChild_HHBUI(std::move(demoButton_));
            mainWindow_->addChild_HHBUI(std::move(demoEdit_));

            return true;

        } catch (...) {
            return false;
        }
    }

    /// 设置事件处理器
    void setupEventHandlers_HHBUI() noexcept {
        try {
            if (!mainWindow_) {
                return;
            }

            // 设置窗口关闭事件处理器
            mainWindow_->registerEventHandler_HHBUI(WM_CLOSE, [this](HWND, UINT, WPARAM, LPARAM) -> LRESULT {
                exitApplication_HHBUI(0);
                return 0;
            });

            // 查找按钮控件并设置事件处理器
            auto* button = dynamic_cast<Button_HHBUI*>(mainWindow_->findChild_HHBUI(1001));
            if (button) {
                button->registerEventHandler_HHBUI(ButtonEvent_HHBUI::Click, [this](ButtonEvent_HHBUI, const Any&) {
                    onButtonClick_HHBUI();
                });
            }

            // 查找编辑框控件并设置事件处理器
            auto* edit = dynamic_cast<Edit_HHBUI*>(mainWindow_->findChild_HHBUI(1002));
            if (edit) {
                edit->registerEventHandler_HHBUI(EditEvent_HHBUI::TextChanged, [this](EditEvent_HHBUI, const Any&) {
                    onEditTextChanged_HHBUI();
                });
            }

        } catch (...) {
            std::wcerr << L"Exception occurred while setting up event handlers!" << std::endl;
        }
    }

    /// 按钮点击事件处理
    void onButtonClick_HHBUI() noexcept {
        try {
            static int32_t clickCount = 0;
            ++clickCount;

            String message = StringUtils_HHBUI::format_HHBUI(L"Button clicked {} times!", clickCount);
            
            // 更新按钮文本
            auto* button = dynamic_cast<Button_HHBUI*>(mainWindow_->findChild_HHBUI(1001));
            if (button) {
                button->setText_HHBUI(message);
            }

            std::wcout << L"Button clicked: " << message << std::endl;

        } catch (...) {
            std::wcerr << L"Exception occurred in button click handler!" << std::endl;
        }
    }

    /// 编辑框文本变更事件处理
    void onEditTextChanged_HHBUI() noexcept {
        try {
            auto* edit = dynamic_cast<Edit_HHBUI*>(mainWindow_->findChild_HHBUI(1002));
            if (edit) {
                const String& text = edit->getText_HHBUI();
                std::wcout << L"Edit text changed: " << text << std::endl;
            }

        } catch (...) {
            std::wcerr << L"Exception occurred in edit text changed handler!" << std::endl;
        }
    }

private:
    UniquePtr<Window_HHBUI> mainWindow_{};
    UniquePtr<Button_HHBUI> demoButton_{};
    UniquePtr<Edit_HHBUI> demoEdit_{};
};

/// 应用程序入口点
int WINAPI wWinMain(
    _In_ HINSTANCE hInstance,
    _In_opt_ HINSTANCE hPrevInstance,
    _In_ LPWSTR lpCmdLine,
    _In_ int nCmdShow
) {
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    try {
        // 创建演示应用程序
        ModernDemoApp_HHBUI app;

        // 初始化应用程序
        if (!app.initialize_HHBUI()) {
            std::wcerr << L"Failed to initialize demo application!" << std::endl;
            return -1;
        }

        // 运行应用程序
        int32_t result = app.run_HHBUI();

        // 关闭应用程序
        app.shutdown_HHBUI();

        return result;

    } catch (const Exception_HHBUI& e) {
        std::wcerr << L"HHBUI Exception in main: " << e.format_HHBUI() << std::endl;
        return -1;
    } catch (const std::exception& e) {
        std::wcerr << L"Standard Exception in main: " << StringUtils_HHBUI::utf8ToUtf16_HHBUI(e.what()).value_or(L"Unknown error") << std::endl;
        return -1;
    } catch (...) {
        std::wcerr << L"Unknown exception in main!" << std::endl;
        return -1;
    }
}

/// 控制台应用程序入口点（用于测试）
int main() {
    try {
        std::wcout << L"HHBUI Modern Framework Demo" << std::endl;
        std::wcout << L"Version: " << getFrameworkVersion_HHBUI() << std::endl;
        std::wcout << L"Built with C++17 standard" << std::endl;
        std::wcout << L"" << std::endl;

        // 演示兼容性检查
        auto& compatManager = CompatibilityManager_HHBUI::getInstance_HHBUI();
        auto compatResult = compatManager.checkApiCompatibility_HHBUI("UIEngine::Init");
        
        std::wcout << L"Compatibility Check:" << std::endl;
        std::wcout << L"  API: UIEngine::Init" << std::endl;
        std::wcout << L"  Compatible: " << (compatResult.isCompatible ? L"Yes" : L"No") << std::endl;
        std::wcout << L"  Warning Level: " << static_cast<int>(compatResult.warningLevel) << std::endl;
        std::wcout << L"" << std::endl;

        // 演示字符串工具
        String testStr = L"  Hello, HHBUI Modern Framework!  ";
        String trimmedStr = StringUtils_HHBUI::trim_HHBUI(testStr);
        String upperStr = StringUtils_HHBUI::toUpper_HHBUI(trimmedStr);
        
        std::wcout << L"String Utils Demo:" << std::endl;
        std::wcout << L"  Original: '" << testStr << L"'" << std::endl;
        std::wcout << L"  Trimmed: '" << trimmedStr << L"'" << std::endl;
        std::wcout << L"  Upper: '" << upperStr << L"'" << std::endl;
        std::wcout << L"" << std::endl;

        // 演示内存管理
        auto smartPtr = allocateUnique_HHBUI<int>(42);
        std::wcout << L"Memory Management Demo:" << std::endl;
        std::wcout << L"  Smart pointer value: " << (smartPtr ? *smartPtr : 0) << std::endl;
        std::wcout << L"" << std::endl;

        std::wcout << L"Demo completed successfully!" << std::endl;
        return 0;

    } catch (const Exception_HHBUI& e) {
        std::wcerr << L"HHBUI Exception: " << e.format_HHBUI() << std::endl;
        return -1;
    } catch (const std::exception& e) {
        std::wcerr << L"Standard Exception: " << StringUtils_HHBUI::utf8ToUtf16_HHBUI(e.what()).value_or(L"Unknown error") << std::endl;
        return -1;
    } catch (...) {
        std::wcerr << L"Unknown exception!" << std::endl;
        return -1;
    }
}
