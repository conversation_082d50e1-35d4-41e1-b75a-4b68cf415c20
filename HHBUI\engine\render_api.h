﻿/**
** =====================================================================================
**
**       文件名称: render_api.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】高级渲染引擎API接口定义 - 现代化DirectX11渲染框架 （声明文件）
**
**       主要功能:
**       - 高性能DirectX11渲染管线接口系统
**       - 现代化着色器管理与编译框架
**       - 智能缓冲区与纹理资源管理
**       - 高级渲染状态与管线配置
**       - 跨平台渲染抽象层设计
**       - COM接口规范与引用计数管理
**       - 渲染性能监控与调试支持
**
**       技术特性:
**       - 采用现代C++17标准与COM接口规范
**       - DirectX11硬件加速渲染管线
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能着色器编译与缓存系统
**       - 多线程安全的渲染资源管理
**       - 实时渲染性能监控与统计
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建现代化DirectX11渲染API接口
**                             2. 实现COM接口规范与引用计数
**                             3. 添加高级着色器管理系统
**                             4. 支持智能缓冲区与纹理管理
**                             5. 集成渲染状态与管线配置
**                             6. 添加性能监控与调试接口
**                             7. 确保跨平台兼容性设计
**
** =====================================================================================
**/

#pragma once
#include "engine/object_api.h"
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <guiddef.h>

// 确保PURE宏定义
#ifndef PURE
#define PURE = 0
#endif

namespace HHBUI
{
	// 前向声明
	class UIShader;
	class UIBuffer;
	class UITexture;
	class UIRenderState;

	// 接口前向声明
	struct IBuffer;
	struct IShader;
	struct ITexture;
	struct IRenderState;
	struct IRenderManager;
	struct IRenderFactory;

	// 接口IID定义
	DEFINE_GUID(IID_IRenderObject, 0x5C75E0DD, 0x96FD, 0x4902, 0xA3, 0xE0, 0x98, 0x65, 0x4C, 0xAB, 0x12, 0xCE);
	DEFINE_GUID(IID_IShader, 0xA1B2C3D4, 0xE5F6, 0x7890, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90);
	DEFINE_GUID(IID_IBuffer, 0xB2C3D4E5, 0xF617, 0x8901, 0xBC, 0xDE, 0xF2, 0x34, 0x56, 0x78, 0x90, 0x12);
	DEFINE_GUID(IID_ITexture, 0xC3D4E5F6, 0xA7B8, 0x9012, 0xCD, 0xEF, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34);
	DEFINE_GUID(IID_IRenderState, 0xD4E5F6A7, 0xB8C9, 0x0123, 0xDE, 0xFA, 0x45, 0x67, 0x89, 0x01, 0x23, 0x45);
	DEFINE_GUID(IID_IRenderManager, 0xE5F6A7B8, 0xC9D0, 0x1234, 0xEF, 0xAB, 0x56, 0x78, 0x90, 0x12, 0x34, 0x56);
	DEFINE_GUID(IID_IRenderFactory, 0xF6A7B8C9, 0xD0E1, 0x2345, 0xFA, 0xBC, 0x67, 0x89, 0x01, 0x23, 0x45, 0x67);

	/// 渲染器类型枚举
	enum class RenderType : uint32_t
	{
		D2D_ONLY = 0,      // 仅使用D2D渲染
		D3D_ONLY = 1,      // 仅使用D3D渲染
		HYBRID = 2,        // 混合渲染模式
		GDI_COMPAT = 3     // GDI兼容模式
	};

	/// 着色器类型枚举
	enum class ShaderType : uint32_t
	{
		VERTEX = 0,
		PIXEL = 1,
		GEOMETRY = 2,
		HULL = 3,
		DOMAIN_SHADER = 4,
		COMPUTE = 5
	};

	/// 缓冲区类型枚举
	enum class BufferType : uint32_t
	{
		VERTEX = 0,
		INDEX = 1,
		CONSTANT = 2,
		STRUCTURED = 3,
		STAGING = 4
	};

	/// 渲染统计信息
	struct RenderStats
	{
		uint32_t draw_calls;           // 绘制调用次数
		uint32_t triangles;            // 三角形数量
		uint32_t vertices;             // 顶点数量
		uint64_t gpu_memory_used;      // GPU内存使用量
		float frame_time_ms;           // 帧时间(毫秒)
		float gpu_time_ms;             // GPU时间(毫秒)
	};

	/// 渲染器对象基础接口
	EXINTERFACE("5C75E0DD-96FD-4902-A3E0-98654CAB12CE") IRenderObject : public IObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 获取渲染器类型
		EXMETHOD RenderType GetRenderType() const PURE;

		/// 获取渲染统计信息
		EXMETHOD const RenderStats& GetRenderStats() const PURE;

		/// 重置渲染统计
		EXMETHOD void ResetRenderStats() PURE;
	};

	/// 着色器接口
	EXINTERFACE("A1B2C3D4-E5F6-7890-ABCD-EF1234567890") IShader : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 编译着色器
		EXMETHOD HRESULT Compile(LPCWSTR source_code, LPCSTR entry_point, LPCSTR target) PURE;

		/// 从文件加载着色器
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path, LPCSTR entry_point, LPCSTR target) PURE;

		/// 绑定着色器到渲染管线
		EXMETHOD HRESULT Bind() PURE;

		/// 解绑着色器
		EXMETHOD void Unbind() PURE;

		/// 获取着色器类型
		EXMETHOD ShaderType GetShaderType() const PURE;

		/// 设置常量缓冲区
		EXMETHOD HRESULT SetConstantBuffer(uint32_t slot, IBuffer* buffer) PURE;
	};

	/// 缓冲区接口
	EXINTERFACE("B2C3D4E5-F617-8901-BCDE-F23456789012") IBuffer : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建缓冲区
		EXMETHOD HRESULT Create(BufferType type, uint32_t size, const void* initial_data = nullptr,
			bool dynamic = false, bool cpu_access = false) PURE;

		/// 更新缓冲区数据
		EXMETHOD HRESULT UpdateData(const void* data, uint32_t size, uint32_t offset = 0) PURE;

		/// 映射缓冲区内存
		EXMETHOD HRESULT Map(void** mapped_data, bool read_only = false) PURE;

		/// 解除映射
		EXMETHOD void Unmap() PURE;

		/// 绑定到渲染管线
		EXMETHOD HRESULT Bind(uint32_t slot) PURE;

		/// 获取缓冲区大小
		EXMETHOD uint32_t GetSize() const PURE;

		/// 获取缓冲区类型
		EXMETHOD BufferType GetBufferType() const PURE;
	};

	/// 纹理接口
	EXINTERFACE("C3D4E5F6-A7B8-9012-CDEF-345678901234") ITexture : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建2D纹理
		EXMETHOD HRESULT Create2D(uint32_t width, uint32_t height, DXGI_FORMAT format,
			const void* initial_data = nullptr, bool render_target = false, bool shader_resource = true) PURE;

		/// 从文件加载纹理
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path) PURE;

		/// 绑定为着色器资源
		EXMETHOD HRESULT BindAsShaderResource(uint32_t slot) PURE;

		/// 绑定为渲染目标
		EXMETHOD HRESULT BindAsRenderTarget() PURE;

		/// 获取纹理尺寸
		EXMETHOD void GetSize(uint32_t* width, uint32_t* height) const PURE;

		/// 获取纹理格式
		EXMETHOD DXGI_FORMAT GetFormat() const PURE;
	};

	/// 渲染状态接口
	EXINTERFACE("D4E5F6A7-B8C9-0123-DEFA-456789012345") IRenderState : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 设置混合状态
		EXMETHOD HRESULT SetBlendState(bool enable, D3D11_BLEND src_blend = D3D11_BLEND_SRC_ALPHA,
			D3D11_BLEND dest_blend = D3D11_BLEND_INV_SRC_ALPHA) PURE;

		/// 设置深度模板状态
		EXMETHOD HRESULT SetDepthStencilState(bool depth_enable, bool depth_write_enable = true,
			D3D11_COMPARISON_FUNC depth_func = D3D11_COMPARISON_LESS) PURE;

		/// 设置光栅化状态
		EXMETHOD HRESULT SetRasterizerState(D3D11_CULL_MODE cull_mode = D3D11_CULL_BACK,
			D3D11_FILL_MODE fill_mode = D3D11_FILL_SOLID, bool scissor_enable = false) PURE;

		/// 应用所有状态
		EXMETHOD HRESULT Apply() PURE;
	};

	/// 高级渲染管理器接口
	EXINTERFACE("E5F6A7B8-C9D0-1234-EFAB-567890123456") IRenderManager : public IRenderObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 初始化渲染管理器
		EXMETHOD HRESULT Initialize(RenderType render_type, HWND hwnd = nullptr) PURE;

		/// 关闭渲染管理器
		EXMETHOD void Shutdown() PURE;

		/// 开始帧渲染
		EXMETHOD HRESULT BeginFrame() PURE;

		/// 结束帧渲染
		EXMETHOD HRESULT EndFrame() PURE;

		/// 呈现到屏幕
		EXMETHOD HRESULT Present(bool vsync = true) PURE;

		/// 创建着色器
		EXMETHOD HRESULT CreateShader(ShaderType type, IShader** shader) PURE;

		/// 创建缓冲区
		EXMETHOD HRESULT CreateBuffer(BufferType type, IBuffer** buffer) PURE;

		/// 创建纹理
		EXMETHOD HRESULT CreateTexture(ITexture** texture) PURE;

		/// 创建渲染状态
		EXMETHOD HRESULT CreateRenderState(IRenderState** render_state) PURE;

		/// 设置视口
		EXMETHOD HRESULT SetViewport(float x, float y, float width, float height,
			float min_depth = 0.0f, float max_depth = 1.0f) PURE;

		/// 清除渲染目标
		EXMETHOD HRESULT ClearRenderTarget(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f) PURE;

		/// 清除深度缓冲区
		EXMETHOD HRESULT ClearDepthStencil(float depth = 1.0f, uint8_t stencil = 0) PURE;

		/// 绘制图元
		EXMETHOD HRESULT Draw(uint32_t vertex_count, uint32_t start_vertex = 0) PURE;

		/// 绘制索引图元
		EXMETHOD HRESULT DrawIndexed(uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0) PURE;

		/// 获取D3D11设备
		EXMETHOD ID3D11Device* GetD3D11Device() const PURE;

		/// 获取D3D11设备上下文
		EXMETHOD ID3D11DeviceContext* GetD3D11DeviceContext() const PURE;

		/// 获取D2D设备上下文
		EXMETHOD ID2D1DeviceContext* GetD2D1DeviceContext() const PURE;

		/// 启用/禁用调试模式
		EXMETHOD void SetDebugMode(bool enable) PURE;

		/// 获取GPU内存使用情况
		EXMETHOD uint64_t GetGPUMemoryUsage() const PURE;
	};

	/// 渲染工厂接口
	EXINTERFACE("F6A7B8C9-D0E1-2345-FABC-678901234567") IRenderFactory : public IObject
	{
		// 显式声明IUnknown方法以支持override
		EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) PURE;
		EXMETHOD ULONG AddRef() PURE;
		EXMETHOD ULONG Release() PURE;

		/// 创建渲染管理器
		EXMETHOD HRESULT CreateRenderManager(IRenderManager** render_manager) PURE;

		/// 获取支持的渲染类型
		EXMETHOD uint32_t GetSupportedRenderTypes() const PURE;

		/// 检查功能支持
		EXMETHOD bool IsFeatureSupported(LPCWSTR feature_name) const PURE;
	};

	/// 全局函数声明

	/// 创建渲染工厂
	HRESULT CreateRenderFactory(IRenderFactory** factory);

	/// 获取默认渲染管理器
	IRenderManager* GetDefaultRenderManager();

	/// 设置默认渲染管理器
	void SetDefaultRenderManager(IRenderManager* manager);
}


