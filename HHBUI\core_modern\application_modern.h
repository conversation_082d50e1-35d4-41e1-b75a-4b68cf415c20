/**
** =====================================================================================
**
**       文件名称: application_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化应用程序框架 - 应用程序管理 （声明文件）
**
**       主要功能:
**       - 现代化应用程序生命周期管理
**       - 高性能多线程应用程序架构
**       - 智能配置管理与持久化
**       - 异常安全的应用程序初始化
**       - 跨平台应用程序抽象层
**       - 高级事件循环与消息处理
**       - 应用程序性能监控与调试
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与异常安全保证
**       - 高性能多线程事件处理
**       - 模板元编程与编译时优化
**       - 标准库容器与算法集成
**       - 现代化配置管理系统
**       - 跨平台兼容性设计
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化应用程序框架
**                             2. 实现RAII生命周期管理
**                             3. 添加智能配置管理系统
**                             4. 集成异常安全保证机制
**                             5. 优化多线程事件处理
**                             6. 确保跨平台兼容性
**                             7. 添加性能监控接口
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <functional>
#include <optional>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <Windows.h>

namespace hhbui::modern {

    // 前向声明
    class ApplicationConfig_HHBUI;
    class EventLoop_HHBUI;
    class PerformanceMonitor_HHBUI;

    /// 应用程序初始化信息
    struct ApplicationInitInfo_HHBUI {
        HINSTANCE instanceHandle = nullptr;
        String applicationName = L"HHBUI Application";
        String applicationVersion = L"2.0.0.0";
        float dpiScale = 1.0f;
        bool debugMode = false;
        String defaultFontFace = L"Segoe UI";
        int32_t defaultFontSize = 14;
        uint32_t defaultFontStyle = 0;
        Optional<String> configFilePath = std::nullopt;
        Optional<String> logFilePath = std::nullopt;
        bool enablePerformanceMonitoring = true;
        uint32_t maxWorkerThreads = std::thread::hardware_concurrency();
    };

    /// 应用程序状态枚举
    enum class ApplicationState_HHBUI : uint32_t {
        Uninitialized = 0,
        Initializing = 1,
        Running = 2,
        Paused = 3,
        Stopping = 4,
        Stopped = 5,
        Error = 6
    };

    /// 应用程序事件类型
    enum class ApplicationEvent_HHBUI : uint32_t {
        Initialize = 0,
        Startup = 1,
        Shutdown = 2,
        Pause = 3,
        Resume = 4,
        ConfigChanged = 5,
        LowMemory = 6,
        Error = 7
    };

    /// 应用程序事件处理器
    using ApplicationEventHandler_HHBUI = Function<void(ApplicationEvent_HHBUI, const Any&)>;

    /// 现代化应用程序管理器类
    class Application_HHBUI {
    public:
        /// 构造函数
        Application_HHBUI() noexcept;
        
        /// 析构函数
        ~Application_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        Application_HHBUI(const Application_HHBUI&) = delete;
        Application_HHBUI& operator=(const Application_HHBUI&) = delete;

        /// 启用移动构造和赋值
        Application_HHBUI(Application_HHBUI&&) noexcept = default;
        Application_HHBUI& operator=(Application_HHBUI&&) noexcept = default;

        /// 初始化应用程序
        [[nodiscard]] bool initialize_HHBUI(const ApplicationInitInfo_HHBUI& initInfo) noexcept;

        /// 启动应用程序
        [[nodiscard]] bool startup_HHBUI() noexcept;

        /// 运行应用程序主循环
        [[nodiscard]] int32_t run_HHBUI() noexcept;

        /// 关闭应用程序
        void shutdown_HHBUI() noexcept;

        /// 暂停应用程序
        void pause_HHBUI() noexcept;

        /// 恢复应用程序
        void resume_HHBUI() noexcept;

        /// 获取应用程序状态
        [[nodiscard]] ApplicationState_HHBUI getState_HHBUI() const noexcept;

        /// 检查应用程序是否正在运行
        [[nodiscard]] bool isRunning_HHBUI() const noexcept;

        /// 检查应用程序是否已初始化
        [[nodiscard]] bool isInitialized_HHBUI() const noexcept;

        /// 获取应用程序实例句柄
        [[nodiscard]] HINSTANCE getInstanceHandle_HHBUI() const noexcept;

        /// 获取应用程序名称
        [[nodiscard]] const String& getApplicationName_HHBUI() const noexcept;

        /// 获取应用程序版本
        [[nodiscard]] const String& getApplicationVersion_HHBUI() const noexcept;

        /// 获取DPI缩放系数
        [[nodiscard]] float getDpiScale_HHBUI() const noexcept;

        /// 检查是否为调试模式
        [[nodiscard]] bool isDebugMode_HHBUI() const noexcept;

        /// 获取运行时间（秒）
        [[nodiscard]] double getRunningTime_HHBUI() const noexcept;

        /// 注册事件处理器
        void registerEventHandler_HHBUI(ApplicationEvent_HHBUI eventType, 
                                        ApplicationEventHandler_HHBUI handler) noexcept;

        /// 注销事件处理器
        void unregisterEventHandler_HHBUI(ApplicationEvent_HHBUI eventType) noexcept;

        /// 触发应用程序事件
        void triggerEvent_HHBUI(ApplicationEvent_HHBUI eventType, const Any& eventData = {}) noexcept;

        /// 获取配置管理器
        [[nodiscard]] ApplicationConfig_HHBUI* getConfig_HHBUI() const noexcept;

        /// 获取性能监控器
        [[nodiscard]] PerformanceMonitor_HHBUI* getPerformanceMonitor_HHBUI() const noexcept;

        /// 获取单例实例
        [[nodiscard]] static Application_HHBUI& getInstance_HHBUI() noexcept;

        /// 计算DPI缩放值
        [[nodiscard]] float scaleDpi_HHBUI(float value) const noexcept;

        /// 获取当前时间戳
        [[nodiscard]] static double getCurrentTime_HHBUI() noexcept;

    private:
        /// 内部初始化
        [[nodiscard]] bool internalInitialize_HHBUI(const ApplicationInitInfo_HHBUI& initInfo) noexcept;

        /// 内部清理
        void internalCleanup_HHBUI() noexcept;

        /// 处理Windows消息
        [[nodiscard]] bool processWindowsMessages_HHBUI() noexcept;

        /// 更新应用程序状态
        void updateState_HHBUI(ApplicationState_HHBUI newState) noexcept;

    private:
        std::atomic<ApplicationState_HHBUI> state_{ApplicationState_HHBUI::Uninitialized};
        ApplicationInitInfo_HHBUI initInfo_{};
        UniquePtr<ApplicationConfig_HHBUI> config_{};
        UniquePtr<EventLoop_HHBUI> eventLoop_{};
        UniquePtr<PerformanceMonitor_HHBUI> performanceMonitor_{};
        
        mutable std::shared_mutex eventHandlersMutex_{};
        HashMap<ApplicationEvent_HHBUI, Vector<ApplicationEventHandler_HHBUI>> eventHandlers_{};
        
        std::chrono::steady_clock::time_point startTime_{};
        std::atomic<bool> shouldExit_{false};
        
        static inline Application_HHBUI* instance_{nullptr};
        static inline std::once_flag instanceFlag_{};
    };

    /// 全局函数声明

    /// 获取应用程序实例
    [[nodiscard]] Application_HHBUI& getApplication_HHBUI() noexcept;

    /// 检查应用程序是否已初始化
    [[nodiscard]] bool isApplicationInitialized_HHBUI() noexcept;

    /// 获取应用程序版本字符串
    [[nodiscard]] StringView getApplicationVersion_HHBUI() noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using info_Init = hhbui::modern::ApplicationInitInfo_HHBUI;
    using UIEngine = hhbui::modern::Application_HHBUI;
}
