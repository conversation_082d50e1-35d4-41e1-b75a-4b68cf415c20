# HHBUI 现代化框架 v2.0.0.0

## 概述

HHBUI 现代化框架是基于 C++17 标准的全新 UI 框架，在保持完全向后兼容的基础上，提供了现代化的编程体验、更高的性能和更强的稳定性。

## 主要特性

### 🚀 现代化 C++17 标准
- 采用现代 C++17 标准与最佳实践
- 智能指针与 RAII 自动资源管理
- 异常安全保证与错误恢复机制
- 模板元编程与编译时优化
- 标准库容器与算法集成

### 🎨 高性能图形渲染
- DirectX11/D2D 高性能渲染引擎
- GPU 加速与多线程渲染管线
- 智能资源管理与缓存系统
- 现代化着色器与特效系统
- 跨平台图形抽象层

### 🧩 丰富的 UI 控件库
- 30+ 种现代化 UI 控件
- 智能布局管理与自适应调整
- 高级动画与过渡效果
- 主题系统与样式定制
- 响应式设计支持

### 🔧 强大的开发工具
- 高精度性能监控与分析
- 多级日志记录与调试系统
- 内存泄漏检测与优化
- 异常安全的错误处理
- 跨平台兼容性支持

### 🔄 完全向后兼容
- 100% 兼容旧版本 API
- 智能类型转换与函数映射
- 渐进式迁移支持
- 兼容性警告与建议系统
- 版本检测与特性支持

## 架构设计

### 模块结构
```
HHBUI_modern/
├── core_modern/           # 核心基础设施
│   ├── application_modern.h    # 应用程序管理
│   ├── config_modern.h         # 配置管理
│   └── exception_modern.h      # 异常处理
├── utils_modern/          # 通用工具类
│   ├── string_modern.h         # 字符串工具
│   ├── memory_modern.h         # 内存管理
│   ├── threading_modern.h      # 线程管理
│   ├── performance_modern.h    # 性能监控
│   └── logging_modern.h        # 日志系统
├── graphics_modern/       # 图形渲染引擎
│   ├── engine_modern.h         # 图形引擎
│   └── render_api_modern.h     # 渲染API
├── ui_modern/            # UI 元素和控件
│   ├── control_base_modern.h   # 控件基类
│   └── window_modern.h         # 窗口管理
├── control_modern/       # 具体控件实现
│   ├── button_modern.h         # 按钮控件
│   └── edit_modern.h           # 编辑框控件
└── compat/              # 向后兼容层
    ├── legacy_headers.h        # 兼容性头文件
    └── legacy_implementation.cpp # 兼容性实现
```

### 命名规范
- **类名**：PascalCase + `_HHBUI` 后缀（如 `Button_HHBUI`）
- **函数名**：camelCase + `_HHBUI` 后缀（如 `createWindow_HHBUI`）
- **变量名**：camelCase（如 `renderContext`）
- **常量**：UPPER_SNAKE_CASE + `_HHBUI` 后缀（如 `MAX_BUFFER_SIZE_HHBUI`）
- **命名空间**：小写（如 `hhbui::modern`）

## 快速开始

### 1. 包含头文件
```cpp
#include "hhbui_modern.h"
using namespace hhbui::modern;
```

### 2. 初始化框架
```cpp
ApplicationInitInfo_HHBUI initInfo{};
initInfo.instanceHandle = GetModuleHandle(nullptr);
initInfo.applicationName = L"My Modern App";
initInfo.debugMode = true;

if (!initializeFramework_HHBUI(initInfo)) {
    // 处理初始化失败
    return -1;
}
```

### 3. 创建窗口
```cpp
WindowCreateInfo_HHBUI createInfo{};
createInfo.title = L"Modern HHBUI Window";
createInfo.bounds = UIRect_HHBUI{100, 100, 800, 600};

auto window = createWindow_HHBUI(createInfo);
if (!window) {
    // 处理创建失败
    return -1;
}
```

### 4. 创建控件
```cpp
// 创建按钮
auto button = createStyledButton_HHBUI(L"Click Me!", ButtonStyle_HHBUI::Primary);
button->initialize_HHBUI(window.get(), UIRect_HHBUI{50, 50, 200, 100});

// 创建编辑框
auto edit = createEdit_HHBUI(EditType_HHBUI::SingleLine, L"Enter text...");
edit->initialize_HHBUI(window.get(), UIRect_HHBUI{50, 120, 400, 160});

// 添加到窗口
window->addChild_HHBUI(std::move(button));
window->addChild_HHBUI(std::move(edit));
```

### 5. 运行应用程序
```cpp
window->show_HHBUI();
int result = runApplication_HHBUI();
shutdownFramework_HHBUI();
return result;
```

## 向后兼容性

### 旧版本 API 支持
```cpp
// 旧版本代码仍然可以正常工作
#include "hhbui.h"

HHBUI::info_Init initInfo{};
initInfo.hInstance = GetModuleHandle(nullptr);
HHBUI::Init(&initInfo);

HWND hwnd = HHBUI::CreateWnd(nullptr, L"MyClass", L"My Window", 
                            WS_OVERLAPPEDWINDOW, 100, 100, 800, 600, nullptr);
HHBUI::ShowWnd(hwnd, SW_SHOW);

int result = HHBUI::MessageLoop();
HHBUI::UnInit();
```

### 兼容性警告系统
框架会自动检测旧版本 API 的使用，并提供现代化建议：
```
[HHBUI Compatibility Warning] HHBUI::Init: Using legacy initialization API
  Suggestion: Consider using hhbui::modern::initializeFramework_HHBUI
  Modern Alternative: hhbui::modern::initializeFramework_HHBUI
```

## 性能监控

### 启用性能监控
```cpp
auto& perfMonitor = getPerformanceMonitor_HHBUI();
perfMonitor.setEnabled_HHBUI(true);
perfMonitor.startMonitoring_HHBUI();

// 创建性能计数器
auto* frameTimeCounter = createPerformanceCounter_HHBUI(
    L"FrameTime", 
    PerformanceCounterType_HHBUI::Timer, 
    PerformanceUnit_HHBUI::Milliseconds
);
```

### 使用性能计时器
```cpp
// 作用域计时器
{
    HHBUI_PERF_SCOPE(L"Render Frame");
    // 渲染代码
}

// 函数执行时间测量
auto [result, elapsed] = measureExecutionTime_HHBUI([]() {
    // 要测量的代码
    return someFunction();
});
```

## 日志系统

### 配置日志记录器
```cpp
auto& logger = getLogger_HHBUI();

// 添加控制台输出器
auto consoleAppender = std::make_unique<ConsoleLogAppender_HHBUI>();
logger.addAppender_HHBUI(std::move(consoleAppender));

// 添加文件输出器
auto fileAppender = std::make_unique<FileLogAppender_HHBUI>(L"app.log");
logger.addAppender_HHBUI(std::move(fileAppender));
```

### 记录日志
```cpp
// 使用便捷宏
HHBUI_LOG_INFO(L"Application started successfully");
HHBUI_LOG_WARNING(L"Configuration file not found, using defaults");
HHBUI_LOG_ERROR(L"Failed to load resource: {}", resourcePath);

// 使用日志记录器对象
logger.info_HHBUI(L"User logged in", L"Authentication");
logger.error_HHBUI(L"Database connection failed", L"Database");
```

## 异常处理

### 异常安全编程
```cpp
try {
    // 可能抛出异常的代码
    auto window = createWindow_HHBUI(createInfo);
    
} catch (const Exception_HHBUI& e) {
    // 处理 HHBUI 异常
    HHBUI_LOG_ERROR(L"HHBUI Exception: {}", e.format_HHBUI());
    
} catch (const std::exception& e) {
    // 处理标准异常
    HHBUI_LOG_ERROR(L"Standard Exception: {}", 
                    StringUtils_HHBUI::utf8ToUtf16_HHBUI(e.what()).value_or(L"Unknown"));
}
```

### 安全函数调用
```cpp
// 异常安全的函数调用
auto result = safeCall_HHBUI([&]() {
    return riskyFunction();
});

if (result) {
    // 使用结果
    processResult(*result);
} else {
    // 处理失败情况
    HHBUI_LOG_WARNING(L"Function call failed safely");
}
```

## 构建要求

### 编译器要求
- **Visual Studio 2019** 或更高版本
- **GCC 8.0** 或更高版本
- **Clang 7.0** 或更高版本
- **C++17** 标准支持

### 依赖库
- **DirectX 11** SDK
- **Direct2D** 1.1 或更高版本
- **DirectWrite** 1.0 或更高版本
- **Windows SDK** 10.0 或更高版本

### 编译选项
```cmake
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 启用现代化特性
add_definitions(-DHHBUI_ENABLE_MODERN_FEATURES=1)
add_definitions(-DHHBUI_ENABLE_LEGACY_SUPPORT=1)

# 链接库
target_link_libraries(your_target 
    d3d11.lib
    d2d1.lib
    dwrite.lib
    dxgi.lib
)
```

## 迁移指南

### 从旧版本迁移
1. **保持现有代码不变**：旧版本 API 完全兼容
2. **逐步采用新特性**：在新代码中使用现代化 API
3. **关注兼容性警告**：根据建议逐步迁移
4. **利用新功能**：性能监控、日志系统、异常处理等

### 推荐迁移步骤
1. 包含新的头文件 `hhbui_modern.h`
2. 使用现代化初始化 API
3. 采用智能指针管理资源
4. 启用性能监控和日志记录
5. 使用异常安全的编程模式

## 示例项目

完整的示例项目请参考 `examples/modern_demo.cpp`，展示了：
- 现代化框架初始化
- 窗口和控件创建
- 事件处理机制
- 性能监控使用
- 日志记录配置
- 异常安全编程

## 技术支持

- **官方网站**：https://hhbui.com/
- **文档中心**：https://docs.hhbui.com/
- **GitHub 仓库**：https://github.com/hhbui/hhbui-modern
- **技术论坛**：https://forum.hhbui.com/

## 版本历史

### v2.0.0.0 (2025-07-30)
- 🎉 全新现代化 C++17 框架
- 🚀 高性能 DirectX11/D2D 渲染引擎
- 🧩 丰富的现代化 UI 控件库
- 🔧 强大的开发工具集成
- 🔄 完全向后兼容保证
- 📊 性能监控与分析系统
- 📝 多级日志记录系统
- 🛡️ 异常安全保证机制

---

**HHBUI 现代化框架** - 让 UI 开发更现代、更高效、更安全！
