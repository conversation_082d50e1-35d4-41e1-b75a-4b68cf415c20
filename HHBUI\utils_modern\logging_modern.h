/**
** =====================================================================================
**
**       文件名称: logging_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化日志记录系统 - 日志记录 （声明文件）
**
**       主要功能:
**       - 现代化多级日志记录与管理系统
**       - 高性能异步日志写入与缓冲
**       - 智能日志格式化与过滤机制
**       - 异常安全的日志操作接口
**       - 跨平台日志记录抽象层
**       - 日志轮转与存储管理
**       - 现代化日志分析与监控
**
**       技术特性:
**       - 采用现代C++17标准与异步IO
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能无锁队列与多线程
**       - 模板元编程与编译时优化
**       - 标准库格式化与IO集成
**       - 跨平台文件系统抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化日志记录系统
**                             2. 实现高性能异步日志机制
**                             3. 添加智能日志格式化系统
**                             4. 集成异常安全保证机制
**                             5. 优化日志记录性能
**                             6. 确保跨平台兼容性
**                             7. 添加日志分析功能
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <fstream>
#include <sstream>
#include <chrono>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <queue>
#include <vector>
#include <unordered_map>
#include <functional>
#include <optional>
#include <filesystem>
#include <source_location>

namespace hhbui::modern {

    // 前向声明
    class LogFormatter_HHBUI;
    class LogAppender_HHBUI;
    class LogFilter_HHBUI;

    /// 日志级别枚举
    enum class LogLevel_HHBUI : uint32_t {
        Trace = 0,      // 跟踪信息
        Debug = 1,      // 调试信息
        Info = 2,       // 一般信息
        Warning = 3,    // 警告信息
        Error = 4,      // 错误信息
        Critical = 5,   // 严重错误
        Off = 6         // 关闭日志
    };

    /// 日志输出目标枚举
    enum class LogTarget_HHBUI : uint32_t {
        Console = 1,    // 控制台输出
        File = 2,       // 文件输出
        Memory = 4,     // 内存缓冲
        Network = 8,    // 网络输出
        System = 16,    // 系统日志
        Custom = 32     // 自定义输出
    };

    /// 日志记录结构
    struct LogRecord_HHBUI {
        LogLevel_HHBUI level = LogLevel_HHBUI::Info;
        String message;
        String category;
        std::chrono::system_clock::time_point timestamp = std::chrono::system_clock::now();
        std::thread::id threadId = std::this_thread::get_id();
        std::source_location location = std::source_location::current();
        HashMap<String, String> properties;
        
        /// 格式化日志记录
        [[nodiscard]] String format_HHBUI(const LogFormatter_HHBUI* formatter = nullptr) const noexcept;
    };

    /// 日志格式化器接口
    class LogFormatter_HHBUI {
    public:
        virtual ~LogFormatter_HHBUI() = default;
        
        /// 格式化日志记录
        [[nodiscard]] virtual String format_HHBUI(const LogRecord_HHBUI& record) const noexcept = 0;
        
        /// 获取格式化器名称
        [[nodiscard]] virtual StringView getName_HHBUI() const noexcept = 0;
    };

    /// 默认日志格式化器
    class DefaultLogFormatter_HHBUI : public LogFormatter_HHBUI {
    public:
        /// 构造函数
        explicit DefaultLogFormatter_HHBUI(StringView pattern = L"[{timestamp}] [{level}] [{category}] {message}") noexcept;
        
        /// 格式化日志记录
        [[nodiscard]] String format_HHBUI(const LogRecord_HHBUI& record) const noexcept override;
        
        /// 获取格式化器名称
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        
        /// 设置格式模式
        void setPattern_HHBUI(StringView pattern) noexcept;
        
        /// 获取格式模式
        [[nodiscard]] const String& getPattern_HHBUI() const noexcept;
        
    private:
        String pattern_;
        mutable std::mutex mutex_;
    };

    /// 日志输出器接口
    class LogAppender_HHBUI {
    public:
        virtual ~LogAppender_HHBUI() = default;
        
        /// 输出日志记录
        virtual void append_HHBUI(const LogRecord_HHBUI& record) noexcept = 0;
        
        /// 刷新输出缓冲
        virtual void flush_HHBUI() noexcept = 0;
        
        /// 关闭输出器
        virtual void close_HHBUI() noexcept = 0;
        
        /// 获取输出器名称
        [[nodiscard]] virtual StringView getName_HHBUI() const noexcept = 0;
        
        /// 设置格式化器
        virtual void setFormatter_HHBUI(UniquePtr<LogFormatter_HHBUI> formatter) noexcept = 0;
        
        /// 获取格式化器
        [[nodiscard]] virtual LogFormatter_HHBUI* getFormatter_HHBUI() const noexcept = 0;
    };

    /// 控制台日志输出器
    class ConsoleLogAppender_HHBUI : public LogAppender_HHBUI {
    public:
        /// 构造函数
        explicit ConsoleLogAppender_HHBUI(bool useStderr = false) noexcept;
        
        /// 析构函数
        ~ConsoleLogAppender_HHBUI() noexcept override;
        
        /// 输出日志记录
        void append_HHBUI(const LogRecord_HHBUI& record) noexcept override;
        
        /// 刷新输出缓冲
        void flush_HHBUI() noexcept override;
        
        /// 关闭输出器
        void close_HHBUI() noexcept override;
        
        /// 获取输出器名称
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        
        /// 设置格式化器
        void setFormatter_HHBUI(UniquePtr<LogFormatter_HHBUI> formatter) noexcept override;
        
        /// 获取格式化器
        [[nodiscard]] LogFormatter_HHBUI* getFormatter_HHBUI() const noexcept override;
        
        /// 启用/禁用颜色输出
        void setColorEnabled_HHBUI(bool enabled) noexcept;
        
        /// 检查颜色输出是否启用
        [[nodiscard]] bool isColorEnabled_HHBUI() const noexcept;
        
    private:
        /// 获取级别颜色代码
        [[nodiscard]] StringView getLevelColor_HHBUI(LogLevel_HHBUI level) const noexcept;
        
    private:
        bool useStderr_;
        std::atomic<bool> colorEnabled_{true};
        UniquePtr<LogFormatter_HHBUI> formatter_;
        mutable std::mutex mutex_;
    };

    /// 文件日志输出器
    class FileLogAppender_HHBUI : public LogAppender_HHBUI {
    public:
        /// 构造函数
        explicit FileLogAppender_HHBUI(const std::filesystem::path& filePath, bool append = true) noexcept;
        
        /// 析构函数
        ~FileLogAppender_HHBUI() noexcept override;
        
        /// 输出日志记录
        void append_HHBUI(const LogRecord_HHBUI& record) noexcept override;
        
        /// 刷新输出缓冲
        void flush_HHBUI() noexcept override;
        
        /// 关闭输出器
        void close_HHBUI() noexcept override;
        
        /// 获取输出器名称
        [[nodiscard]] StringView getName_HHBUI() const noexcept override;
        
        /// 设置格式化器
        void setFormatter_HHBUI(UniquePtr<LogFormatter_HHBUI> formatter) noexcept override;
        
        /// 获取格式化器
        [[nodiscard]] LogFormatter_HHBUI* getFormatter_HHBUI() const noexcept override;
        
        /// 设置最大文件大小
        void setMaxFileSize_HHBUI(size_t maxSize) noexcept;
        
        /// 获取最大文件大小
        [[nodiscard]] size_t getMaxFileSize_HHBUI() const noexcept;
        
        /// 设置最大备份文件数
        void setMaxBackupFiles_HHBUI(size_t maxBackups) noexcept;
        
        /// 获取最大备份文件数
        [[nodiscard]] size_t getMaxBackupFiles_HHBUI() const noexcept;
        
        /// 启用/禁用自动刷新
        void setAutoFlush_HHBUI(bool enabled) noexcept;
        
        /// 检查自动刷新是否启用
        [[nodiscard]] bool isAutoFlush_HHBUI() const noexcept;
        
    private:
        /// 检查并执行文件轮转
        void checkAndRotate_HHBUI() noexcept;
        
        /// 轮转日志文件
        void rotateFile_HHBUI() noexcept;
        
    private:
        std::filesystem::path filePath_;
        std::wofstream fileStream_;
        UniquePtr<LogFormatter_HHBUI> formatter_;
        
        size_t maxFileSize_ = 10 * 1024 * 1024; // 10MB
        size_t maxBackupFiles_ = 5;
        std::atomic<bool> autoFlush_{true};
        
        mutable std::mutex mutex_;
    };

    /// 现代化日志记录器类
    class Logger_HHBUI {
    public:
        /// 构造函数
        explicit Logger_HHBUI(StringView name = L"HHBUI_Logger") noexcept;
        
        /// 析构函数
        ~Logger_HHBUI() noexcept;
        
        /// 禁用拷贝构造和赋值
        Logger_HHBUI(const Logger_HHBUI&) = delete;
        Logger_HHBUI& operator=(const Logger_HHBUI&) = delete;
        
        /// 启用移动构造和赋值
        Logger_HHBUI(Logger_HHBUI&&) noexcept = default;
        Logger_HHBUI& operator=(Logger_HHBUI&&) noexcept = default;
        
        /// 记录日志
        void log_HHBUI(
            LogLevel_HHBUI level,
            StringView message,
            StringView category = L"",
            std::source_location location = std::source_location::current()
        ) noexcept;
        
        /// 格式化记录日志
        template<typename... Args>
        void logFormat_HHBUI(
            LogLevel_HHBUI level,
            StringView format,
            Args&&... args,
            StringView category = L"",
            std::source_location location = std::source_location::current()
        ) noexcept;
        
        /// 便捷日志记录方法
        void trace_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        void debug_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        void info_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        void warning_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        void error_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        void critical_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
        
        /// 设置日志级别
        void setLevel_HHBUI(LogLevel_HHBUI level) noexcept;
        
        /// 获取日志级别
        [[nodiscard]] LogLevel_HHBUI getLevel_HHBUI() const noexcept;
        
        /// 检查日志级别是否启用
        [[nodiscard]] bool isLevelEnabled_HHBUI(LogLevel_HHBUI level) const noexcept;
        
        /// 添加日志输出器
        void addAppender_HHBUI(UniquePtr<LogAppender_HHBUI> appender) noexcept;
        
        /// 移除日志输出器
        void removeAppender_HHBUI(StringView name) noexcept;
        
        /// 获取日志输出器
        [[nodiscard]] LogAppender_HHBUI* getAppender_HHBUI(StringView name) const noexcept;
        
        /// 清除所有输出器
        void clearAppenders_HHBUI() noexcept;
        
        /// 启用/禁用异步日志
        void setAsyncEnabled_HHBUI(bool enabled) noexcept;
        
        /// 检查异步日志是否启用
        [[nodiscard]] bool isAsyncEnabled_HHBUI() const noexcept;
        
        /// 刷新所有输出器
        void flush_HHBUI() noexcept;
        
        /// 获取日志记录器名称
        [[nodiscard]] const String& getName_HHBUI() const noexcept;
        
        /// 获取全局日志记录器
        [[nodiscard]] static Logger_HHBUI& getGlobalLogger_HHBUI() noexcept;
        
    private:
        /// 异步日志处理线程
        void asyncLogThread_HHBUI() noexcept;
        
        /// 处理日志记录
        void processLogRecord_HHBUI(const LogRecord_HHBUI& record) noexcept;
        
    private:
        String name_;
        std::atomic<LogLevel_HHBUI> level_{LogLevel_HHBUI::Info};
        
        mutable std::shared_mutex appendersMutex_;
        HashMap<String, UniquePtr<LogAppender_HHBUI>> appenders_;
        
        std::atomic<bool> asyncEnabled_{true};
        std::queue<LogRecord_HHBUI> logQueue_;
        std::mutex queueMutex_;
        std::condition_variable queueCondition_;
        
        UniquePtr<std::thread> asyncThread_;
        std::atomic<bool> shouldStopAsync_{false};
        
        static inline UniquePtr<Logger_HHBUI> globalLogger_{};
        static inline std::once_flag globalLoggerFlag_{};
    };

    /// 全局日志记录函数

    /// 获取全局日志记录器
    [[nodiscard]] Logger_HHBUI& getLogger_HHBUI() noexcept;

    /// 便捷日志记录函数
    void logTrace_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
    void logDebug_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
    void logInfo_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
    void logWarning_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
    void logError_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;
    void logCritical_HHBUI(StringView message, StringView category = L"", std::source_location location = std::source_location::current()) noexcept;

    /// 日志记录宏定义
    #define HHBUI_LOG_TRACE(message, ...) \
        hhbui::modern::logTrace_HHBUI(message, ##__VA_ARGS__)

    #define HHBUI_LOG_DEBUG(message, ...) \
        hhbui::modern::logDebug_HHBUI(message, ##__VA_ARGS__)

    #define HHBUI_LOG_INFO(message, ...) \
        hhbui::modern::logInfo_HHBUI(message, ##__VA_ARGS__)

    #define HHBUI_LOG_WARNING(message, ...) \
        hhbui::modern::logWarning_HHBUI(message, ##__VA_ARGS__)

    #define HHBUI_LOG_ERROR(message, ...) \
        hhbui::modern::logError_HHBUI(message, ##__VA_ARGS__)

    #define HHBUI_LOG_CRITICAL(message, ...) \
        hhbui::modern::logCritical_HHBUI(message, ##__VA_ARGS__)

    // ========== 模板实现 ==========

    template<typename... Args>
    void Logger_HHBUI::logFormat_HHBUI(
        LogLevel_HHBUI level,
        StringView format,
        Args&&... args,
        StringView category,
        std::source_location location
    ) noexcept {
        if (!isLevelEnabled_HHBUI(level)) {
            return;
        }
        
        try {
            String message = StringUtils_HHBUI::format_HHBUI(format, std::forward<Args>(args)...);
            log_HHBUI(level, message, category, location);
        } catch (...) {
            // 日志记录失败时不应该抛出异常
            log_HHBUI(LogLevel_HHBUI::Error, L"Failed to format log message", L"Logger", location);
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using Logger = hhbui::modern::Logger_HHBUI;
    using LogLevel = hhbui::modern::LogLevel_HHBUI;
    using LogRecord = hhbui::modern::LogRecord_HHBUI;
    using LogFormatter = hhbui::modern::LogFormatter_HHBUI;
    using LogAppender = hhbui::modern::LogAppender_HHBUI;
}
