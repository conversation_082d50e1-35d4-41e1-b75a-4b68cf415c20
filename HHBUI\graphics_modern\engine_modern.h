/**
** =====================================================================================
**
**       文件名称: engine_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化图形引擎系统 - 图形引擎 （声明文件）
**
**       主要功能:
**       - 现代化DirectX11/D2D图形渲染引擎
**       - 高性能GPU加速渲染管线
**       - 智能资源管理与缓存系统
**       - 异常安全的图形操作接口
**       - 跨平台图形抽象与兼容
**       - 图形性能监控与调试支持
**       - 现代化着色器与特效系统
**
**       技术特性:
**       - 采用现代C++17标准与COM接口
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能GPU内存管理优化
**       - 模板元编程与编译时优化
**       - 多线程渲染与并行处理
**       - 跨平台图形API抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化图形引擎系统
**                             2. 实现智能资源管理机制
**                             3. 添加高性能渲染管线
**                             4. 集成异常安全保证机制
**                             5. 优化GPU内存管理
**                             6. 确保跨平台兼容性
**                             7. 添加图形调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <unordered_map>
#include <optional>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <Windows.h>
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <dxgi1_2.h>
#include <wrl/client.h>

namespace hhbui::modern {

    // 前向声明
    class RenderContext_HHBUI;
    class ResourceManager_HHBUI;
    class PerformanceProfiler_HHBUI;

    /// 图形设备类型枚举
    enum class GraphicsDeviceType_HHBUI : uint32_t {
        Hardware = 0,       // 硬件加速
        Software = 1,       // 软件渲染
        Reference = 2,      // 参考设备
        Warp = 3           // WARP设备
    };

    /// 渲染质量级别
    enum class RenderQuality_HHBUI : uint32_t {
        Low = 0,
        Medium = 1,
        High = 2,
        Ultra = 3
    };

    /// 抗锯齿模式
    enum class AntiAliasingMode_HHBUI : uint32_t {
        None = 0,
        MSAA_2X = 1,
        MSAA_4X = 2,
        MSAA_8X = 3,
        FXAA = 4,
        TAA = 5
    };

    /// 垂直同步模式
    enum class VSyncMode_HHBUI : uint32_t {
        Disabled = 0,
        Enabled = 1,
        Adaptive = 2,
        Fast = 3
    };

    /// 图形引擎初始化信息
    struct GraphicsEngineInitInfo_HHBUI {
        HWND targetWindow = nullptr;
        GraphicsDeviceType_HHBUI deviceType = GraphicsDeviceType_HHBUI::Hardware;
        RenderQuality_HHBUI quality = RenderQuality_HHBUI::High;
        AntiAliasingMode_HHBUI antiAliasing = AntiAliasingMode_HHBUI::MSAA_4X;
        VSyncMode_HHBUI vsync = VSyncMode_HHBUI::Enabled;
        bool debugMode = false;
        bool enableMultithreading = true;
        uint32_t maxFrameRate = 60;
        uint32_t backBufferWidth = 0;   // 0 = 使用窗口大小
        uint32_t backBufferHeight = 0;  // 0 = 使用窗口大小
        DXGI_FORMAT backBufferFormat = DXGI_FORMAT_R8G8B8A8_UNORM;
        uint32_t maxTextureSize = 8192;
        size_t videoMemoryBudget = 0;    // 0 = 自动检测
    };

    /// 图形引擎统计信息
    struct GraphicsEngineStats_HHBUI {
        uint32_t frameCount = 0;
        float frameRate = 0.0f;
        float frameTime = 0.0f;
        float gpuTime = 0.0f;
        uint32_t drawCalls = 0;
        uint32_t triangles = 0;
        uint32_t vertices = 0;
        size_t videoMemoryUsed = 0;
        size_t videoMemoryAvailable = 0;
        size_t textureMemoryUsed = 0;
        size_t bufferMemoryUsed = 0;
        std::chrono::steady_clock::time_point lastUpdate;
    };

    /// 现代化图形引擎类
    class GraphicsEngine_HHBUI {
    public:
        /// 构造函数
        GraphicsEngine_HHBUI() noexcept;

        /// 析构函数
        ~GraphicsEngine_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        GraphicsEngine_HHBUI(const GraphicsEngine_HHBUI&) = delete;
        GraphicsEngine_HHBUI& operator=(const GraphicsEngine_HHBUI&) = delete;

        /// 启用移动构造和赋值
        GraphicsEngine_HHBUI(GraphicsEngine_HHBUI&&) noexcept = default;
        GraphicsEngine_HHBUI& operator=(GraphicsEngine_HHBUI&&) noexcept = default;

        /// 初始化图形引擎
        [[nodiscard]] bool initialize_HHBUI(const GraphicsEngineInitInfo_HHBUI& initInfo) noexcept;

        /// 关闭图形引擎
        void shutdown_HHBUI() noexcept;

        /// 检查是否已初始化
        [[nodiscard]] bool isInitialized_HHBUI() const noexcept;

        /// 开始帧渲染
        [[nodiscard]] bool beginFrame_HHBUI() noexcept;

        /// 结束帧渲染
        [[nodiscard]] bool endFrame_HHBUI() noexcept;

        /// 呈现到屏幕
        [[nodiscard]] bool present_HHBUI(bool vsync = true) noexcept;

        /// 清除渲染目标
        void clearRenderTarget_HHBUI(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f) noexcept;

        /// 清除深度缓冲区
        void clearDepthStencil_HHBUI(float depth = 1.0f, uint8_t stencil = 0) noexcept;

        /// 设置视口
        void setViewport_HHBUI(float x, float y, float width, float height, float minDepth = 0.0f, float maxDepth = 1.0f) noexcept;

        /// 获取视口
        [[nodiscard]] D3D11_VIEWPORT getViewport_HHBUI() const noexcept;

        /// 调整后缓冲区大小
        [[nodiscard]] bool resizeBackBuffer_HHBUI(uint32_t width, uint32_t height) noexcept;

        /// 获取后缓冲区大小
        [[nodiscard]] std::pair<uint32_t, uint32_t> getBackBufferSize_HHBUI() const noexcept;

        /// 设置渲染质量
        void setRenderQuality_HHBUI(RenderQuality_HHBUI quality) noexcept;

        /// 获取渲染质量
        [[nodiscard]] RenderQuality_HHBUI getRenderQuality_HHBUI() const noexcept;

        /// 设置抗锯齿模式
        void setAntiAliasingMode_HHBUI(AntiAliasingMode_HHBUI mode) noexcept;

        /// 获取抗锯齿模式
        [[nodiscard]] AntiAliasingMode_HHBUI getAntiAliasingMode_HHBUI() const noexcept;

        /// 设置垂直同步模式
        void setVSyncMode_HHBUI(VSyncMode_HHBUI mode) noexcept;

        /// 获取垂直同步模式
        [[nodiscard]] VSyncMode_HHBUI getVSyncMode_HHBUI() const noexcept;

        /// 设置最大帧率
        void setMaxFrameRate_HHBUI(uint32_t maxFps) noexcept;

        /// 获取最大帧率
        [[nodiscard]] uint32_t getMaxFrameRate_HHBUI() const noexcept;

        /// 启用/禁用调试模式
        void setDebugMode_HHBUI(bool enabled) noexcept;

        /// 检查是否为调试模式
        [[nodiscard]] bool isDebugMode_HHBUI() const noexcept;

        /// 获取D3D11设备
        [[nodiscard]] ID3D11Device* getD3D11Device_HHBUI() const noexcept;

        /// 获取D3D11设备上下文
        [[nodiscard]] ID3D11DeviceContext* getD3D11DeviceContext_HHBUI() const noexcept;

        /// 获取D2D设备上下文
        [[nodiscard]] ID2D1DeviceContext* getD2D1DeviceContext_HHBUI() const noexcept;

        /// 获取DWrite工厂
        [[nodiscard]] IDWriteFactory* getDWriteFactory_HHBUI() const noexcept;

        /// 获取DXGI交换链
        [[nodiscard]] IDXGISwapChain1* getSwapChain_HHBUI() const noexcept;

        /// 获取渲染上下文
        [[nodiscard]] RenderContext_HHBUI* getRenderContext_HHBUI() const noexcept;

        /// 获取资源管理器
        [[nodiscard]] ResourceManager_HHBUI* getResourceManager_HHBUI() const noexcept;

        /// 获取性能分析器
        [[nodiscard]] PerformanceProfiler_HHBUI* getPerformanceProfiler_HHBUI() const noexcept;

        /// 获取引擎统计信息
        [[nodiscard]] GraphicsEngineStats_HHBUI getStats_HHBUI() const noexcept;

        /// 重置统计信息
        void resetStats_HHBUI() noexcept;

        /// 截屏到文件
        [[nodiscard]] bool captureScreenshot_HHBUI(StringView filePath) noexcept;

        /// 截屏到内存
        [[nodiscard]] Optional<Vector<uint8_t>> captureScreenshotToMemory_HHBUI() noexcept;

        /// 检查功能支持
        [[nodiscard]] bool isFeatureSupported_HHBUI(StringView featureName) const noexcept;

        /// 获取适配器信息
        [[nodiscard]] String getAdapterInfo_HHBUI() const noexcept;

        /// 获取驱动版本
        [[nodiscard]] String getDriverVersion_HHBUI() const noexcept;

        /// 获取单例实例
        [[nodiscard]] static GraphicsEngine_HHBUI& getInstance_HHBUI() noexcept;

        /// 计算DPI缩放值
        [[nodiscard]] float scaleDpi_HHBUI(float value) const noexcept;

        /// 获取DPI缩放系数
        [[nodiscard]] float getDpiScale_HHBUI() const noexcept;

        /// 设置DPI缩放系数
        void setDpiScale_HHBUI(float scale) noexcept;

        /// 获取当前时间
        [[nodiscard]] static float getCurrentTime_HHBUI() noexcept;

        /// 获取版本信息
        [[nodiscard]] static StringView getVersion_HHBUI() noexcept;

    private:
        /// 内部初始化D3D11设备
        [[nodiscard]] bool initializeD3D11_HHBUI(const GraphicsEngineInitInfo_HHBUI& initInfo) noexcept;

        /// 内部初始化D2D设备
        [[nodiscard]] bool initializeD2D_HHBUI() noexcept;

        /// 内部初始化DWrite
        [[nodiscard]] bool initializeDWrite_HHBUI() noexcept;

        /// 内部初始化交换链
        [[nodiscard]] bool initializeSwapChain_HHBUI(const GraphicsEngineInitInfo_HHBUI& initInfo) noexcept;

        /// 内部清理资源
        void internalCleanup_HHBUI() noexcept;

        /// 更新统计信息
        void updateStats_HHBUI() noexcept;

        /// 检查设备丢失
        [[nodiscard]] bool checkDeviceLost_HHBUI() noexcept;

        /// 恢复设备
        [[nodiscard]] bool recoverDevice_HHBUI() noexcept;

    private:
        // COM智能指针
        Microsoft::WRL::ComPtr<ID3D11Device> d3d11Device_{};
        Microsoft::WRL::ComPtr<ID3D11DeviceContext> d3d11DeviceContext_{};
        Microsoft::WRL::ComPtr<ID2D1Device> d2d1Device_{};
        Microsoft::WRL::ComPtr<ID2D1DeviceContext> d2d1DeviceContext_{};
        Microsoft::WRL::ComPtr<IDWriteFactory> dwriteFactory_{};
        Microsoft::WRL::ComPtr<IDXGISwapChain1> swapChain_{};
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> renderTargetView_{};
        Microsoft::WRL::ComPtr<ID3D11DepthStencilView> depthStencilView_{};

        // 引擎状态
        std::atomic<bool> initialized_{false};
        std::atomic<bool> debugMode_{false};
        GraphicsEngineInitInfo_HHBUI initInfo_{};
        
        // 渲染状态
        D3D11_VIEWPORT viewport_{};
        std::atomic<float> dpiScale_{1.0f};
        
        // 子系统
        UniquePtr<RenderContext_HHBUI> renderContext_{};
        UniquePtr<ResourceManager_HHBUI> resourceManager_{};
        UniquePtr<PerformanceProfiler_HHBUI> performanceProfiler_{};
        
        // 统计信息
        mutable std::shared_mutex statsMutex_{};
        GraphicsEngineStats_HHBUI stats_{};
        std::chrono::steady_clock::time_point frameStartTime_{};
        
        // 单例
        static inline GraphicsEngine_HHBUI* instance_{nullptr};
        static inline std::once_flag instanceFlag_{};
    };

    /// 全局图形引擎函数

    /// 获取图形引擎实例
    [[nodiscard]] GraphicsEngine_HHBUI& getGraphicsEngine_HHBUI() noexcept;

    /// 检查图形引擎是否已初始化
    [[nodiscard]] bool isGraphicsEngineInitialized_HHBUI() noexcept;

    /// 获取图形引擎版本
    [[nodiscard]] StringView getGraphicsEngineVersion_HHBUI() noexcept;

    /// 检查DirectX功能级别支持
    [[nodiscard]] bool checkDirectXFeatureLevel_HHBUI(D3D_FEATURE_LEVEL requiredLevel) noexcept;

    /// 获取可用的图形适配器列表
    [[nodiscard]] Vector<String> getAvailableAdapters_HHBUI() noexcept;

    /// 获取推荐的图形设置
    [[nodiscard]] GraphicsEngineInitInfo_HHBUI getRecommendedGraphicsSettings_HHBUI(HWND targetWindow) noexcept;

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    using info_Init = hhbui::modern::GraphicsEngineInitInfo_HHBUI;
    using UIEngine = hhbui::modern::GraphicsEngine_HHBUI;
    using UIDrawContext = hhbui::modern::RenderContext_HHBUI;
}
