/**
** =====================================================================================
**
**       文件名称: memory_modern.h
**       创建时间: 2025-07-30
**       文件描述: 【HHBUI】现代化内存管理系统 - 内存管理 （声明文件）
**
**       主要功能:
**       - 现代化智能指针与RAII内存管理
**       - 高性能内存池与对象池系统
**       - 智能内存分配器与垃圾回收
**       - 异常安全的内存操作接口
**       - 内存泄漏检测与调试支持
**       - 跨平台内存管理抽象层
**       - 内存使用统计与性能监控
**
**       技术特性:
**       - 采用现代C++17标准智能指针
**       - RAII自动资源管理与异常安全
**       - 高性能内存池与缓存优化
**       - 模板元编程与编译时优化
**       - 原子操作与线程安全保证
**       - 标准库分配器接口兼容
**       - 跨平台内存管理抽象
**
**       更新记录:
**       2025-07-30 v2.0.0.0 : 1. 创建现代化内存管理系统
**                             2. 实现智能指针与RAII管理
**                             3. 添加高性能内存池系统
**                             4. 集成异常安全保证机制
**                             5. 优化内存分配性能
**                             6. 确保线程安全操作
**                             7. 添加内存调试支持
**
** =====================================================================================
**/

#pragma once

#include <memory>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <queue>
#include <stack>
#include <functional>
#include <type_traits>
#include <chrono>
#include <thread>
#include <optional>
#include <string>
#include <string_view>

namespace hhbui::modern {

    // 前向声明
    template<typename T>
    class ObjectPool_HHBUI;
    
    template<typename T>
    class MemoryPool_HHBUI;
    
    class MemoryTracker_HHBUI;

    /// 内存分配策略
    enum class AllocationStrategy_HHBUI : uint32_t {
        Default = 0,        // 默认分配器
        Pool = 1,          // 内存池分配
        Stack = 2,         // 栈分配器
        Linear = 3,        // 线性分配器
        FreeList = 4,      // 自由列表分配器
        Buddy = 5          // 伙伴系统分配器
    };

    /// 内存对齐选项
    enum class MemoryAlignment_HHBUI : size_t {
        Default = sizeof(void*),
        Byte1 = 1,
        Byte2 = 2,
        Byte4 = 4,
        Byte8 = 8,
        Byte16 = 16,
        Byte32 = 32,
        Byte64 = 64,
        CacheLine = 64,    // 典型缓存行大小
        Page = 4096        // 典型页面大小
    };

    /// 内存统计信息
    struct MemoryStats_HHBUI {
        size_t totalAllocated = 0;      // 总分配内存
        size_t totalDeallocated = 0;    // 总释放内存
        size_t currentUsage = 0;        // 当前使用内存
        size_t peakUsage = 0;          // 峰值使用内存
        size_t allocationCount = 0;     // 分配次数
        size_t deallocationCount = 0;   // 释放次数
        size_t fragmentationRatio = 0;  // 碎片化比率
        std::chrono::steady_clock::time_point lastUpdate;
    };

    /// 现代化智能指针别名
    template<typename T>
    using UniquePtr_HHBUI = std::unique_ptr<T>;

    template<typename T>
    using SharedPtr_HHBUI = std::shared_ptr<T>;

    template<typename T>
    using WeakPtr_HHBUI = std::weak_ptr<T>;

    /// 自定义删除器类型
    template<typename T>
    using CustomDeleter_HHBUI = std::function<void(T*)>;

    /// 现代化内存管理器类
    class MemoryManager_HHBUI {
    public:
        /// 构造函数
        MemoryManager_HHBUI() noexcept;

        /// 析构函数
        ~MemoryManager_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        MemoryManager_HHBUI(const MemoryManager_HHBUI&) = delete;
        MemoryManager_HHBUI& operator=(const MemoryManager_HHBUI&) = delete;

        /// 分配内存
        [[nodiscard]] void* allocate_HHBUI(
            size_t size,
            MemoryAlignment_HHBUI alignment = MemoryAlignment_HHBUI::Default,
            AllocationStrategy_HHBUI strategy = AllocationStrategy_HHBUI::Default
        ) noexcept;

        /// 释放内存
        void deallocate_HHBUI(void* ptr, size_t size = 0) noexcept;

        /// 重新分配内存
        [[nodiscard]] void* reallocate_HHBUI(
            void* ptr,
            size_t oldSize,
            size_t newSize,
            MemoryAlignment_HHBUI alignment = MemoryAlignment_HHBUI::Default
        ) noexcept;

        /// 分配对齐内存
        [[nodiscard]] void* allocateAligned_HHBUI(
            size_t size,
            size_t alignment
        ) noexcept;

        /// 释放对齐内存
        void deallocateAligned_HHBUI(void* ptr) noexcept;

        /// 创建智能指针
        template<typename T, typename... Args>
        [[nodiscard]] UniquePtr_HHBUI<T> makeUnique_HHBUI(Args&&... args) noexcept;

        template<typename T, typename... Args>
        [[nodiscard]] SharedPtr_HHBUI<T> makeShared_HHBUI(Args&&... args) noexcept;

        /// 创建带自定义删除器的智能指针
        template<typename T, typename Deleter>
        [[nodiscard]] UniquePtr_HHBUI<T> makeUniqueWithDeleter_HHBUI(T* ptr, Deleter deleter) noexcept;

        /// 获取内存统计信息
        [[nodiscard]] MemoryStats_HHBUI getStats_HHBUI() const noexcept;

        /// 重置统计信息
        void resetStats_HHBUI() noexcept;

        /// 启用/禁用内存跟踪
        void setTrackingEnabled_HHBUI(bool enabled) noexcept;

        /// 检查是否启用内存跟踪
        [[nodiscard]] bool isTrackingEnabled_HHBUI() const noexcept;

        /// 检查内存泄漏
        [[nodiscard]] bool checkMemoryLeaks_HHBUI() const noexcept;

        /// 获取内存泄漏报告
        [[nodiscard]] String getMemoryLeakReport_HHBUI() const noexcept;

        /// 强制垃圾回收
        void forceGarbageCollection_HHBUI() noexcept;

        /// 设置内存限制
        void setMemoryLimit_HHBUI(size_t limitBytes) noexcept;

        /// 获取内存限制
        [[nodiscard]] size_t getMemoryLimit_HHBUI() const noexcept;

        /// 获取单例实例
        [[nodiscard]] static MemoryManager_HHBUI& getInstance_HHBUI() noexcept;

    private:
        /// 内部分配实现
        [[nodiscard]] void* internalAllocate_HHBUI(size_t size, size_t alignment) noexcept;

        /// 内部释放实现
        void internalDeallocate_HHBUI(void* ptr, size_t size) noexcept;

        /// 更新统计信息
        void updateStats_HHBUI(size_t allocatedSize, bool isAllocation) noexcept;

    private:
        mutable std::shared_mutex statsMutex_{};
        MemoryStats_HHBUI stats_{};
        std::atomic<bool> trackingEnabled_{true};
        std::atomic<size_t> memoryLimit_{SIZE_MAX};
        
        UniquePtr_HHBUI<MemoryTracker_HHBUI> tracker_{};
        
        static inline MemoryManager_HHBUI* instance_{nullptr};
        static inline std::once_flag instanceFlag_{};
    };

    /// 高性能对象池模板类
    template<typename T>
    class ObjectPool_HHBUI {
    public:
        /// 构造函数
        explicit ObjectPool_HHBUI(size_t initialSize = 16, size_t maxSize = 1024) noexcept;

        /// 析构函数
        ~ObjectPool_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        ObjectPool_HHBUI(const ObjectPool_HHBUI&) = delete;
        ObjectPool_HHBUI& operator=(const ObjectPool_HHBUI&) = delete;

        /// 启用移动构造和赋值
        ObjectPool_HHBUI(ObjectPool_HHBUI&&) noexcept = default;
        ObjectPool_HHBUI& operator=(ObjectPool_HHBUI&&) noexcept = default;

        /// 获取对象
        template<typename... Args>
        [[nodiscard]] SharedPtr_HHBUI<T> acquire_HHBUI(Args&&... args) noexcept;

        /// 归还对象
        void release_HHBUI(SharedPtr_HHBUI<T> obj) noexcept;

        /// 预分配对象
        void preallocate_HHBUI(size_t count) noexcept;

        /// 清理未使用的对象
        void cleanup_HHBUI() noexcept;

        /// 获取池统计信息
        struct PoolStats_HHBUI {
            size_t totalObjects = 0;
            size_t availableObjects = 0;
            size_t usedObjects = 0;
            size_t maxObjects = 0;
            size_t acquisitionCount = 0;
            size_t releaseCount = 0;
        };
        [[nodiscard]] PoolStats_HHBUI getStats_HHBUI() const noexcept;

    private:
        /// 创建新对象
        [[nodiscard]] SharedPtr_HHBUI<T> createObject_HHBUI() noexcept;

        /// 重置对象状态
        void resetObject_HHBUI(T* obj) noexcept;

    private:
        mutable std::mutex poolMutex_{};
        std::queue<SharedPtr_HHBUI<T>> availableObjects_{};
        std::unordered_set<SharedPtr_HHBUI<T>> usedObjects_{};
        
        size_t maxSize_;
        PoolStats_HHBUI stats_{};
    };

    /// 高性能内存池模板类
    template<typename T>
    class MemoryPool_HHBUI {
    public:
        /// 构造函数
        explicit MemoryPool_HHBUI(size_t blockCount = 1024) noexcept;

        /// 析构函数
        ~MemoryPool_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        MemoryPool_HHBUI(const MemoryPool_HHBUI&) = delete;
        MemoryPool_HHBUI& operator=(const MemoryPool_HHBUI&) = delete;

        /// 分配内存块
        [[nodiscard]] T* allocate_HHBUI() noexcept;

        /// 释放内存块
        void deallocate_HHBUI(T* ptr) noexcept;

        /// 获取池容量
        [[nodiscard]] size_t capacity_HHBUI() const noexcept;

        /// 获取已使用块数
        [[nodiscard]] size_t used_HHBUI() const noexcept;

        /// 获取可用块数
        [[nodiscard]] size_t available_HHBUI() const noexcept;

        /// 扩展池大小
        void expand_HHBUI(size_t additionalBlocks) noexcept;

        /// 收缩池大小
        void shrink_HHBUI() noexcept;

    private:
        /// 初始化内存池
        void initialize_HHBUI(size_t blockCount) noexcept;

        /// 清理内存池
        void cleanup_HHBUI() noexcept;

    private:
        struct Block {
            alignas(T) char data[sizeof(T)];
            Block* next = nullptr;
        };

        std::mutex poolMutex_{};
        Block* freeList_ = nullptr;
        std::vector<UniquePtr_HHBUI<Block[]>> chunks_{};
        size_t blockCount_ = 0;
        size_t usedCount_ = 0;
    };

    /// RAII内存保护器
    class MemoryGuard_HHBUI {
    public:
        /// 构造函数
        explicit MemoryGuard_HHBUI(void* ptr, size_t size = 0) noexcept;

        /// 析构函数
        ~MemoryGuard_HHBUI() noexcept;

        /// 禁用拷贝构造和赋值
        MemoryGuard_HHBUI(const MemoryGuard_HHBUI&) = delete;
        MemoryGuard_HHBUI& operator=(const MemoryGuard_HHBUI&) = delete;

        /// 启用移动构造和赋值
        MemoryGuard_HHBUI(MemoryGuard_HHBUI&& other) noexcept;
        MemoryGuard_HHBUI& operator=(MemoryGuard_HHBUI&& other) noexcept;

        /// 释放保护
        void release_HHBUI() noexcept;

        /// 获取指针
        [[nodiscard]] void* get_HHBUI() const noexcept;

        /// 获取大小
        [[nodiscard]] size_t size_HHBUI() const noexcept;

    private:
        void* ptr_ = nullptr;
        size_t size_ = 0;
    };

    /// 全局内存管理函数

    /// 安全分配内存
    template<typename T>
    [[nodiscard]] UniquePtr_HHBUI<T> allocateUnique_HHBUI() noexcept;

    template<typename T, typename... Args>
    [[nodiscard]] UniquePtr_HHBUI<T> allocateUnique_HHBUI(Args&&... args) noexcept;

    template<typename T>
    [[nodiscard]] SharedPtr_HHBUI<T> allocateShared_HHBUI() noexcept;

    template<typename T, typename... Args>
    [[nodiscard]] SharedPtr_HHBUI<T> allocateShared_HHBUI(Args&&... args) noexcept;

    /// 安全释放指针
    template<typename T>
    void safeDelete_HHBUI(T*& ptr) noexcept;

    template<typename T>
    void safeDeleteArray_HHBUI(T*& ptr) noexcept;

    /// 内存清零
    void secureZeroMemory_HHBUI(void* ptr, size_t size) noexcept;

    /// 获取内存使用情况
    [[nodiscard]] MemoryStats_HHBUI getGlobalMemoryStats_HHBUI() noexcept;

    // ========== 模板实现 ==========

    template<typename T, typename... Args>
    UniquePtr_HHBUI<T> MemoryManager_HHBUI::makeUnique_HHBUI(Args&&... args) noexcept {
        try {
            return std::make_unique<T>(std::forward<Args>(args)...);
        } catch (...) {
            return nullptr;
        }
    }

    template<typename T, typename... Args>
    SharedPtr_HHBUI<T> MemoryManager_HHBUI::makeShared_HHBUI(Args&&... args) noexcept {
        try {
            return std::make_shared<T>(std::forward<Args>(args)...);
        } catch (...) {
            return nullptr;
        }
    }

    template<typename T, typename Deleter>
    UniquePtr_HHBUI<T> MemoryManager_HHBUI::makeUniqueWithDeleter_HHBUI(T* ptr, Deleter deleter) noexcept {
        try {
            return UniquePtr_HHBUI<T>(ptr, deleter);
        } catch (...) {
            if (ptr) {
                deleter(ptr);
            }
            return nullptr;
        }
    }

    template<typename T>
    UniquePtr_HHBUI<T> allocateUnique_HHBUI() noexcept {
        return MemoryManager_HHBUI::getInstance_HHBUI().makeUnique_HHBUI<T>();
    }

    template<typename T, typename... Args>
    UniquePtr_HHBUI<T> allocateUnique_HHBUI(Args&&... args) noexcept {
        return MemoryManager_HHBUI::getInstance_HHBUI().makeUnique_HHBUI<T>(std::forward<Args>(args)...);
    }

    template<typename T>
    SharedPtr_HHBUI<T> allocateShared_HHBUI() noexcept {
        return MemoryManager_HHBUI::getInstance_HHBUI().makeShared_HHBUI<T>();
    }

    template<typename T, typename... Args>
    SharedPtr_HHBUI<T> allocateShared_HHBUI(Args&&... args) noexcept {
        return MemoryManager_HHBUI::getInstance_HHBUI().makeShared_HHBUI<T>(std::forward<Args>(args)...);
    }

    template<typename T>
    void safeDelete_HHBUI(T*& ptr) noexcept {
        if (ptr) {
            delete ptr;
            ptr = nullptr;
        }
    }

    template<typename T>
    void safeDeleteArray_HHBUI(T*& ptr) noexcept {
        if (ptr) {
            delete[] ptr;
            ptr = nullptr;
        }
    }

} // namespace hhbui::modern

// 向后兼容性别名
namespace HHBUI {
    template<typename T>
    using ExAutoPtr = hhbui::modern::SharedPtr_HHBUI<T>;
    
    using MemoryManager = hhbui::modern::MemoryManager_HHBUI;
    
    template<typename T>
    using ObjectPool = hhbui::modern::ObjectPool_HHBUI<T>;
    
    template<typename T>
    using MemoryPool = hhbui::modern::MemoryPool_HHBUI<T>;
}
